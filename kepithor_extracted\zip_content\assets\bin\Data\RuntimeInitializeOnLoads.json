{"root": [{"assemblyName": "Assembly-CSharp", "nameSpace": "Gadsme", "className": "RegisterNativeInterface", "methodName": "registerInterface", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp-firstpass", "nameSpace": "Audiomob.Unmanaged", "className": "AudiomobPluginInitialization", "methodName": "RegisterInitializationHook", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp-firstpass", "nameSpace": "Audiomob.Unmanaged", "className": "AudiomobPluginInitialization", "methodName": "InitializePluginAutomatically", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "io.sentry.unity.runtime", "nameSpace": "Sentry.Unity", "className": "SentryInitialization", "methodName": "Init", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "io.sentry.unity.runtime", "nameSpace": "Sentry.Unity", "className": "StartupTracingIntegration", "methodName": "AfterAssembliesLoaded", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "io.sentry.unity.runtime", "nameSpace": "Sentry.Unity", "className": "StartupTracingIntegration", "methodName": "BeforeSplashScreen", "loadTypes": 3, "isUnityClass": false}, {"assemblyName": "io.sentry.unity.runtime", "nameSpace": "Sentry.Unity", "className": "StartupTracingIntegration", "methodName": "BeforeSceneLoad", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "io.sentry.unity.runtime", "nameSpace": "Sentry.Unity", "className": "StartupTracingIntegration", "methodName": "AfterSceneLoad", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Unity.2D.Animation.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.2D.SpriteShape.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}]}