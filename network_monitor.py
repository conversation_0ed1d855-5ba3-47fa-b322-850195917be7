#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Network Traffic Monitor for Kepithor Rewards
ضبط و تحلیل ترافیک شبکه برای شناسایی API endpoints
"""

import json
import time
import threading
from datetime import datetime
from mitmproxy import http, ctx
from mitmproxy.tools.dump import DumpMaster
from mitmproxy.options import Options

class KepithorTrafficMonitor:
    """Monitor network traffic for Kepithor app"""
    
    def __init__(self):
        """Initialize traffic monitor"""
        self.captured_requests = []
        self.api_endpoints = set()
        self.headers_patterns = {}
        self.auth_tokens = []
        
    def request(self, flow: http.HTTPFlow) -> None:
        """Capture HTTP requests"""
        try:
            # Check if request is related to Kepithor
            if self.is_kepithor_request(flow.request):
                request_data = {
                    'timestamp': datetime.now().isoformat(),
                    'method': flow.request.method,
                    'url': flow.request.pretty_url,
                    'host': flow.request.pretty_host,
                    'path': flow.request.path,
                    'headers': dict(flow.request.headers),
                    'content': self.safe_decode_content(flow.request.content),
                    'query_params': dict(flow.request.query) if flow.request.query else {}
                }
                
                self.captured_requests.append(request_data)
                self.api_endpoints.add(f"{flow.request.method} {flow.request.path}")
                
                # Extract potential auth tokens
                self.extract_auth_info(flow.request.headers)
                
                print(f"🔍 Captured: {flow.request.method} {flow.request.pretty_url}")
                
        except Exception as e:
            print(f"❌ Error capturing request: {e}")
    
    def response(self, flow: http.HTTPFlow) -> None:
        """Capture HTTP responses"""
        try:
            if self.is_kepithor_request(flow.request):
                # Find the corresponding request
                for req in reversed(self.captured_requests):
                    if (req['url'] == flow.request.pretty_url and 
                        req['method'] == flow.request.method):
                        
                        # Add response data
                        req['response'] = {
                            'status_code': flow.response.status_code,
                            'headers': dict(flow.response.headers),
                            'content': self.safe_decode_content(flow.response.content),
                            'size': len(flow.response.content) if flow.response.content else 0
                        }
                        
                        print(f"✅ Response: {flow.response.status_code} for {flow.request.pretty_url}")
                        break
                        
        except Exception as e:
            print(f"❌ Error capturing response: {e}")
    
    def is_kepithor_request(self, request):
        """Check if request is related to Kepithor"""
        kepithor_indicators = [
            'kepithor',
            'reward',
            'faucet',
            'kkt',
            'api',
            # Add more patterns based on findings
        ]
        
        url_lower = request.pretty_url.lower()
        host_lower = request.pretty_host.lower()
        
        return any(indicator in url_lower or indicator in host_lower 
                  for indicator in kepithor_indicators)
    
    def safe_decode_content(self, content):
        """Safely decode content to string"""
        if not content:
            return ""
        
        try:
            # Try UTF-8 first
            decoded = content.decode('utf-8')
            
            # Try to parse as JSON
            try:
                return json.loads(decoded)
            except json.JSONDecodeError:
                return decoded
                
        except UnicodeDecodeError:
            try:
                # Try latin-1 as fallback
                return content.decode('latin-1')
            except:
                # Return hex representation if all else fails
                return content.hex()
    
    def extract_auth_info(self, headers):
        """Extract authentication information from headers"""
        auth_headers = ['authorization', 'x-auth-token', 'x-api-key', 'token', 'bearer']
        
        for header_name, header_value in headers.items():
            if any(auth_header in header_name.lower() for auth_header in auth_headers):
                if header_value not in self.auth_tokens:
                    self.auth_tokens.append({
                        'header': header_name,
                        'value': header_value,
                        'timestamp': datetime.now().isoformat()
                    })
                    print(f"🔑 Found auth token: {header_name}")
    
    def generate_report(self):
        """Generate analysis report"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_requests': len(self.captured_requests),
                    'unique_endpoints': len(self.api_endpoints),
                    'auth_tokens_found': len(self.auth_tokens)
                },
                'api_endpoints': list(self.api_endpoints),
                'auth_tokens': self.auth_tokens,
                'captured_requests': self.captured_requests[-50:]  # Last 50 requests
            }
            
            # Save report
            report_file = f"kepithor_traffic_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"📊 Report saved to: {report_file}")
            return report_file
            
        except Exception as e:
            print(f"❌ Error generating report: {e}")
            return None
    
    def print_summary(self):
        """Print summary of captured traffic"""
        print("\n" + "="*60)
        print("📊 KEPITHOR TRAFFIC SUMMARY")
        print("="*60)
        print(f"🔍 Total Requests: {len(self.captured_requests)}")
        print(f"🌐 Unique Endpoints: {len(self.api_endpoints)}")
        print(f"🔑 Auth Tokens: {len(self.auth_tokens)}")
        
        if self.api_endpoints:
            print("\n🎯 API Endpoints Found:")
            for endpoint in sorted(self.api_endpoints):
                print(f"  • {endpoint}")
        
        if self.auth_tokens:
            print("\n🔑 Authentication Tokens:")
            for token in self.auth_tokens:
                print(f"  • {token['header']}: {token['value'][:20]}...")

def start_monitoring():
    """Start traffic monitoring"""
    print("🚀 Starting Kepithor Traffic Monitor...")
    print("📱 Please start the Kepithor app on your device/emulator")
    print("🔍 Monitoring traffic... Press Ctrl+C to stop")
    
    # Create monitor instance
    monitor = KepithorTrafficMonitor()
    
    # Configure mitmproxy options
    opts = Options(
        listen_port=8080,
        confdir="~/.mitmproxy"
    )
    
    # Create and start proxy
    master = DumpMaster(opts)
    master.addons.add(monitor)
    
    try:
        master.run()
    except KeyboardInterrupt:
        print("\n🛑 Stopping monitor...")
        monitor.print_summary()
        monitor.generate_report()
        print("✅ Monitoring stopped!")

class ManualTrafficAnalyzer:
    """Manual traffic analysis from captured data"""
    
    def __init__(self, capture_file=None):
        """Initialize analyzer"""
        self.capture_file = capture_file
        self.requests = []
        
    def load_capture_file(self, file_path):
        """Load captured traffic from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.requests = data.get('captured_requests', [])
            print(f"✅ Loaded {len(self.requests)} requests from {file_path}")
            return True
        except Exception as e:
            print(f"❌ Error loading capture file: {e}")
            return False
    
    def analyze_patterns(self):
        """Analyze patterns in captured traffic"""
        if not self.requests:
            print("❌ No requests to analyze")
            return
        
        print("🔍 Analyzing traffic patterns...")
        
        # Analyze hosts
        hosts = {}
        for req in self.requests:
            host = req.get('host', 'unknown')
            hosts[host] = hosts.get(host, 0) + 1
        
        print(f"\n🌐 Hosts ({len(hosts)}):")
        for host, count in sorted(hosts.items(), key=lambda x: x[1], reverse=True):
            print(f"  • {host}: {count} requests")
        
        # Analyze endpoints
        endpoints = {}
        for req in self.requests:
            endpoint = f"{req.get('method', 'GET')} {req.get('path', '/')}"
            endpoints[endpoint] = endpoints.get(endpoint, 0) + 1
        
        print(f"\n🎯 Endpoints ({len(endpoints)}):")
        for endpoint, count in sorted(endpoints.items(), key=lambda x: x[1], reverse=True):
            print(f"  • {endpoint}: {count} requests")
        
        # Analyze authentication
        auth_patterns = set()
        for req in self.requests:
            headers = req.get('headers', {})
            for header_name in headers:
                if any(auth in header_name.lower() for auth in ['auth', 'token', 'key', 'bearer']):
                    auth_patterns.add(header_name)
        
        if auth_patterns:
            print(f"\n🔑 Auth Headers Found:")
            for pattern in sorted(auth_patterns):
                print(f"  • {pattern}")

def main():
    """Main function"""
    print("🔍 Kepithor Network Monitor")
    print("=" * 50)
    print("1. Start live monitoring")
    print("2. Analyze captured file")
    print("3. Exit")
    
    choice = input("\nSelect option (1-3): ").strip()
    
    if choice == "1":
        start_monitoring()
    elif choice == "2":
        file_path = input("Enter capture file path: ").strip()
        analyzer = ManualTrafficAnalyzer()
        if analyzer.load_capture_file(file_path):
            analyzer.analyze_patterns()
    elif choice == "3":
        print("👋 Goodbye!")
    else:
        print("❌ Invalid choice!")

if __name__ == "__main__":
    main()
