Open Measurement (OM) License, V 1.1


TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION 

1. Definitions.

“Ad Verification” means methods and practices for determining whether ads appear on intended sites, reach the targeted audience, and/or are viewable.

“Approve” “Approved” and “Approval” means Licensor’s written approval, which may be granted or withheld in Licensor’s sole discretion.

“Contribution” means any work of authorship or other intellectual property, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is or was intentionally submitted to the Licensor by a member of the OMWG, or other Contributor, for inclusion in the Work where it will be licensed as part of the Work under the OM License. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the owner as “Not a Contribution.”

“Contributor” means Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

“Creative Access” means a technical option for the Work wherein (a) the measurement vendor JavaScript tags load within a Window that has access to the ad creative, which enables the measurement tags to directly measure and verify ad creatives and (b) the JavaScript functions as a conduit to deliver the measurement vendor script to the video player.

“Derivative Works” means any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work, including without limitation such code as is required for integration of the SDK by an implementer thereof.

“IFrame” or “Inline Frame” means a HTML document embedded inside another HTML document on a website.

“Integrator(s)” means a company or other legal entity that provides the OM SDK as a service to Publishers thru their own software which provides additional advertising services.

“Legal Entity” means the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of more than fifty percent (50%) of the outstanding shares of such entity, or (iii) beneficial ownership of such entity.

“License” or “OM License” means the terms and conditions for use, reproduction, and distribution as defined herein in this Open Measurement License for Native-App Measurement, Version 1.1 or (at Your option) any later version of the Open Measurement License for Native- App Measurement published by Licensor on the Licensor Website. For the avoidance of doubt: (a) this OM License, Version 1.1 has replaced the prior Open Measurement License, Version 1.0 but applies to the exact same code, defined as the Work in each license; (b) You may elect to continue using a version of the Work under the version of the OM License that was published by Licensor on the Licensor Website at the time You initiated such use but must accept and operate under the terms of the current version of the OM License when upgrading to a new version of the Work; (c) if You elect to continue using a prior version of the OM License as permitted herein, You must give any other recipients of the Work a copy of the OM License version you have elected to continue using in connection with any such distribution as required by Section 4 (Redistribution) below; and (d) this License does not apply to other open measurement code made available by the Licensor under other terms, including but not limited to the OM Work for Web Video released under the OM License for Web Video, Version 1.0 or the OMID Client Libraries released under version 2.0 of the Apache License.

“Licensor” means the copyright owners or entity authorized by the copyright owners of the Work that are granting the License, which authorized representative shall initially be IAB Technology Laboratory, Inc.

“Licensor Website” means https://iabtechlab.com/specifications-guidelines/open-measurement- sdk/ or any other website subsequently identified by Licensor.

“Limited Access” means a technical option for the Work wherein (a) the measurement vendor JavaScript tags load into a sandboxed IFrame and (b) measurement scripts that load into any such sandboxed IFrame cannot measure the ad creative directly and, as a result, require the SDK JavaScript to pass measurement events to measurement scripts via the application programming interface (“API”) for the OMID Client Libraries.

“Native App” means any application that uses native device UI frameworks for audio or audiovisual content, navigation, and advertising delivery. Such application may run on client devices that may have their own screen for audiovisual display or speaker for audio delivery or may be directly connected to an external monitor or screen or speaker to display content or deliver audio content. For example, Native Apps include applications installed and executed on devices like iPhones, Android Phones, Roku, Apple TV, Chromecast, Amazon FireTV, Samsung Smart TV, Amazon Alexa, Sonos, Google Home, and gaming consoles like Sony PlayStation and Microsoft Xbox . For the avoidance of doubt, Native App also includes the use of in-app web views that deliver web-based ads. The Tech Lab shall maintain a mapping of devices, operating systems, apps, and SDKs that are compatible with the OM SDK for Native-App Measurement (the “Native App Compatibility Mapping”). The Native App Compatibility Mapping shall be publicly available and published by Tech Lab here < https://iabtechlab.com/standards/open- measurement-sdk/native-app-compatability-mapping>.

“Non- Client System” means a system which aggregates, analyzes, or otherwise processes measurements made by Native Apps, but which is not co-located with client devices running the Native Apps. For the avoidance of doubt, a Non- Client System is not part of any client device that presents the content being measured, but a Non- Client System may be remotely connected to client devices.

“Object” form means any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, minified JavaScript, generated documentation, and conversions to other media types. For the avoidance of doubt, the “Object” form only includes minified JavaScript and does not include JavaScript that has not been minified.

“OMID Client Libraries” means the open measurement interface definition client libraries that may be used in conjunction with the Work that facilitate communication with measurement tags in a standard fashion and are made available under the open source Apache 2.0 license.

“OM SDK” means a software development kit to enable use of the Object form of the Work for Native-App Ad Verification purposes.

“OMWG” means the Open Measurement Working Group.

“Publisher(s)” means the company or other legal entity that: (i) owns the Native App where the advertising being measured by the OM SDK is being displayed; (ii) integrates an Integrator’s advertising monetization SDKs for use in its own Native Apps; and (iii) is not a licensee under this License.

“Publisher Integrator” means the company or other legal entity that: (i) owns the Native App where the advertising being measured by the OM SDK is being displayed; (ii) integrates the OM SDK for use in its own Native Apps; and (iii) is a licensee under this License.

“Source” form means the preferred form for making modifications, including but not limited to software source code, non-minified JavaScript, documentation source, and configuration files. For the avoidance of doubt, the “Source” form only includes JavaScript that has not been minified and does not include minified JavaScript.

“Web Video” means the broadcast and exhibition of audiovisual content via Internet or mobile technology that supports Web Video applications that load and render all viewable content exclusively through HTML5 including desktops, laptops, tablets and mobile phones, any connected TVs (“CTV”), over the top (“OTT”) devices, or gaming consoles (collectively “Web Video Devices”), but excluding any devices where HTML5 video delivery is not supported, or any Native Apps. The Tech Lab shall maintain a mapping of devices, operating systems, apps, and SDKs that are compatible with the OM SDK for Web Video Measurement (the “Web Video Compatibility Mapping”). The Web Video Compatibility Mapping shall be publicly available and published by Tech Lab here < https://iabtechlab.com/standards/open-measurement-sdk/web- video-compatability-mapping >.

“Window” or “Window Object” means a browsing context, as defined by W3C at https://www.w3.org/TR/html5/browsers.html (the “W3C Site”) or any subsequent website that is provided or identified by W3C as a replacement for the W3C Site, in a HTML environment.

“Work” or “OM Work for Native-App Measurement” means the OMWG Native-App Ad Verification software and related SDK made available under this License in Object form (unless otherwise expressly stated herein).

“You” or “Your” means an individual or Legal Entity exercising the permitted rights granted by this License.

2. Grant of Copyright License.

(a) Subject to the terms and conditions of this License, including, without limitation Your compliance with Section 2(b), each Contributor hereby grants to You a perpetual, worldwide, non- exclusive, royalty-free, revocable (for Your breach of this License) copyright license to use, reproduce, publicly display, publicly perform, sublicense, and distribute the Work in Object form only. For clarity, this grant does not include the right to prepare, reproduce, publicly display, publicly perform, sublicense, or distribute Derivative Works.

(b) Notwithstanding the generality of the foregoing, except as otherwise Approved, the License is limited to the use of the Work and Work integrations in Creative Access mode by (i) Publisher Integrators implementing the Object form of the Work in their Native Apps for Native-App Ad Verification purposes and (ii) Integrators implementing within their advertising monetization SDKs that are integrated by Publishers to serve advertisements that are subject to verification via the Work. An Integrator and Publisher Integrator is required to: (a) follow the process described in the IAB Tech Lab Integrator and/or Publisher Onboarding process, available at: https://iabtechlab.com/omsdk/inapp/integration_onboard, and (b) obtain the IAB Tech Lab IVC Certificate, available at https://iabtechlab.com/omsdk/IVC_guide, to the Work in Native Apps. For the avoidance of doubt, this License does not permit use of the Work on Non- Client Systems.

(c) In addition, subject to Approval, this License shall also include use of the Object form of the Work in an Approved proprietary Native-App plug-in, so long as it is either bundled within the official binary that is distributed by or under authority of Licensor, or is otherwise modular / additive to such official binary and does not require modification to the Source form of the Work (whether to the official binary or otherwise).

(d) In addition, You shall have a license to access or use the Source form of the Work only if specifically Approved by Licensor, but then only to the extent Approved and for such limited Approved purpose(s) (such as may be the case for purposes of merging Approved Contributions into the Work), which approval is revocable at any time by Licensor. The Source form of the Work shall be Licensor’s and Contributor’s confidential information, as applicable, and shall not be used, distributed or disclosed except as Approved or authorized under Section 2(e) of this License; provided, however, individual Contributors retain all of their rights to their respective Contributions outside of the Work. In the event of any Source form modifications, whether for bug fixes or otherwise, the modifications must be submitted back to the Licensor as a Contribution within three (3) business days of completion and unless exigent circumstances reasonably require, before any public use, or otherwise upon request by Licensor.

(e) You acknowledge that the Source form of the Work is the confidential information of the Licensor and Contributors, as applicable. You may not disclose the Source form of the Work without prior written consent of Licensor, except (a) to Your employees, subcontractors and professional advisors under a strict duty of confidentiality, (b) to third parties only as expressly provided for herein, or (c) if and as required by court order, law or governmental or regulatory agency (after, if permitted, giving reasonable notice to the Licensor and using commercially reasonable efforts to provide Licensor with the opportunity to seek a protective order or the equivalent (at Licensor’s expense)). In the event that such protective order or other remedy is not obtained, or that Licensor waives compliance with the provisions hereof, You shall furnish only that portion of the Work which You are advised by counsel is legally required to be disclosed, and You shall use commercially reasonable efforts to ensure that confidential treatment shall be afforded such disclosed portion of the Work.

3. Grant of Patent License. Subject to the terms and conditions of this License, including, without limitation, Your compliance with Section 2(b), each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as set forth in this License) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

4. Redistribution. You may reproduce and distribute copies of the Work, subject to the terms and conditions of this License, solely without modifications in any medium, and in Object form only, provided that (a) You give any other recipients of the Work a copy of this License and (b) if the Work includes a “NOTICE” text file as part of its distribution, then any copies of the Work that You distribute must include a copy of that NOTICE file.

5. Submission of Contributions. Any Contribution You intentionally submit, or are otherwise required by this License or an Approval to submit, for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. For the avoidance of doubt, all Contributions to the Work are licensed and not assigned.

6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor nor any Contributor, except as required for reasonable and customary use in describing the origin of the Work.

7. Disclaimer of Warranty. Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES OR CONDITIONS OF TITLE, NON-INFRINGEMENT, MERCHANTABILITY, OR FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any and all risks associated with Your exercise of permissions and rights under this License.

8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Licensor or Contributor be liable to You for any damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if Licensor or any such Contributor has been advised of the possibility of such damages. You are obligated to comply with all applicable local, state, federal, national, and/or international laws and regulations (as applicable) in the course of using the Work, including, without limitation, all applicable data privacy laws and regulations (including, without limitation, any applicable self-regulatory guidelines) relevant to Your collection, use, and transfer of any data collected by the Work. Additionally, You acknowledge that use of the Work must follow appropriate guidance on use of parameters in the SDK, for example, as defined in relevant documentation associated with SDK and Work usage.

9. Indemnity.

(a) You will indemnify, defend (at the Licensor’s option) and hold Licensor, each Contributor and their related Legal Entities harmless against any claim, costs, losses, damages, liabilities, judgments, fees (including reasonable fees of attorneys and other professionals), and expenses arising out of or in connection with any claim, action or proceeding (any and all of which are “Claims”) by a third party arising out of Your (i) breach of the confidentiality provisions set forth in this License, (ii) intentional misconduct with respect to this License or the Work, (iii) unauthorized use or distribution of the Work, and/or (iv) use of the Work to collect, send, and/or use personal data in violation of applicable privacy laws or regulations (including, without limitation, any applicable self-regulatory guidelines).

(b) Except for (i) Claims by a third party arising out of Your intentional misconduct with respect to this License or the Work, including, without limitation, intentional violation of applicable privacy laws or regulations (including, without limitation, any applicable self- regulatory guidelines), and/or (ii) breach of any confidentiality obligation set forth herein, Your liability for defense and indemnity under this License will not exceed $10,000,000.

(c) The indemnified entity may, at its sole discretion, elect for You to assume control of the defense of the Claim for which you are required to indemnify the indemnified entity and related Legal Entities under this Section 9. If the defense or settlement is assumed by You, the indemnified entity may at any time thereafter elect to appoint its own counsel (at its own expense); or the indemnified entity may take over control of the defense and settlement of such Claim. You will not settle any Claim without the indemnified entity’s prior written consent.

10. Accepting Warranty or Additional Liability. While redistributing the Work, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of Licensor or any other Contributor, and only if You agree to indemnify, defend, and hold Licensor, each Contributor and their related Legal Entities harmless for any liability incurred by, or claims asserted against, such Licensor or Contributor or related Legal Entities by reason of your accepting any such warranty or additional liability.

11. Termination.

(a) In the event that You materially breach this License, You shall promptly stop using the Work and submit written notice of the material breach to Licensor using the contact information for Licensor identified on the Licensor Website. If the breach is curable, You may attempt to cure the breach within 30 days. If either (i) You are not able to cure the breach within 30 days to the reasonable satisfaction of Licensor or (ii) the breach is not curable, then this License shall immediately terminate and You must destroy all copies of the Work unless Licensor Approves a different resolution in writing.

(b) If You commit a non-material breach of this License, You shall promptly provide written notice of the breach to Licensor using the contact information for Licensor identified on the Licensor Website and attempt to cure the non-material breach within 30 days. If You fail to cure the non-material breach within the 30 day period, this License shall terminate at the end of such 30 day period and You shall immediately stop using and destroy all copies of the Work unless Licensor Approves a different resolution in writing.

(c) Termination of Your rights under this section does not terminate the licenses of parties who have received copies of the Work or rights from You under this License prior to that termination.


12. Miscellaneous. This License will be binding upon, inure to the benefit of, and be enforceable by and against, the permitted successors and transferees of the parties; provided that any such successor and/or transferee agrees, and by downloading or using the Work agrees, like all other licensees of the Work, to be bound by the terms of this OM License. This License will be governed by, and construed in accordance with, the laws of the State of New York applicable to contracts executed in and to be performed entirely within that state, without reference to conflicts of laws provisions. The parties hereby agree that the exclusive venue for any disputes hereunder will be in the state and federal courts in the City of New York in the State of New York, and the parties waive any objection based on inconvenient forum and/or all other choice of forum issues. This License contains and constitutes the entire agreement among the parties with respect to the subject matter hereof and supersedes all prior negotiations, agreements and understandings of the parties, whether written or oral. If any term or provision of this License is determined to be invalid, illegal or incapable of being enforced by any rule of law, public policy or other reason, all other conditions and provisions of this License will nevertheless remain in full force and effect, and the invalid or unenforceable term or provision will be deemed replaced by a term or provision as determined by a court to be valid and enforceable to the maximum extent possible, and to express the intention of the parties with respect to the invalid or unenforceable term or provision. No failure or delay by any party in exercising any right, power or privilege under this License will operate as a waiver of such right, power or privilege unless such waiver is made in an instrument in writing signed on behalf of the waiving party. A single or partial exercise of any right, power or privilege will not preclude any other or further exercise of such right, power or privilege or the exercise of any other right, power or privilege. The rights and remedies provided in this License will be cumulative and not exclusive of any rights or remedies provided by law. The parties are independent contractors, and this License does not create an agency, partnership, joint venture, or other legal entity. There are no third-party beneficiaries to this License.
