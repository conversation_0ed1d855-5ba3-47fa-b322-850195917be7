#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Android Emulator Farm - 80 Devices Manager
مدیریت مزرعه 80 شبیه‌ساز اندروید
"""

import os
import sys
import json
import time
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import psutil

from android_device import AndroidDevice

class EmulatorFarm:
    """مدیریت مزرعه شبیه‌سازها"""
    
    def __init__(self, max_devices=80):
        """راه‌اندازی مزرعه"""
        self.max_devices = max_devices
        self.devices = {}
        self.running_devices = {}
        
        # آمار
        self.stats = {
            'total_devices': 0,
            'running_devices': 0,
            'installed_apps': 0,
            'active_kepithor': 0,
            'memory_usage': 0,
            'cpu_usage': 0
        }
        
        # ایجاد فولدر اصلی
        self.base_dir = Path("android_farm")
        self.base_dir.mkdir(exist_ok=True)
        
        print(f"🏭 Emulator Farm initialized for {max_devices} devices")
    
    def create_device(self, device_id):
        """ایجاد دستگاه جدید"""
        try:
            # تولید IP منحصر به فرد
            ip_address = self.generate_unique_ip(device_id)
            
            device = AndroidDevice(device_id, ip_address)
            self.devices[device_id] = device
            self.stats['total_devices'] += 1
            
            print(f"📱 Device {device_id} created with IP {ip_address}")
            return device
            
        except Exception as e:
            print(f"❌ Error creating device {device_id}: {e}")
            return None
    
    def generate_unique_ip(self, device_id):
        """تولید IP منحصر به فرد برای هر دستگاه"""
        # استفاده از subnet های مختلف
        subnets = [
            "192.168.1.",
            "192.168.2.", 
            "192.168.3.",
            "192.168.4.",
            "10.0.1.",
            "10.0.2.",
            "172.16.1.",
            "172.16.2."
        ]
        
        subnet_index = (device_id - 1) // 10  # هر 10 دستگاه یک subnet
        subnet = subnets[subnet_index % len(subnets)]
        
        host_part = ((device_id - 1) % 10) + 10  # از 10 تا 19 در هر subnet
        
        return f"{subnet}{host_part}"
    
    def start_device(self, device_id):
        """راه‌اندازی یک دستگاه"""
        try:
            if device_id not in self.devices:
                device = self.create_device(device_id)
                if not device:
                    return False
            
            device = self.devices[device_id]
            
            if device.start_device():
                self.running_devices[device_id] = device
                self.stats['running_devices'] += 1
                return True
            else:
                return False
                
        except Exception as e:
            print(f"❌ Error starting device {device_id}: {e}")
            return False
    
    def stop_device(self, device_id):
        """توقف یک دستگاه"""
        try:
            if device_id in self.running_devices:
                device = self.running_devices[device_id]
                device.shutdown()
                del self.running_devices[device_id]
                self.stats['running_devices'] -= 1
                return True
            else:
                return False
                
        except Exception as e:
            print(f"❌ Error stopping device {device_id}: {e}")
            return False
    
    def start_all_devices(self, progress_callback=None):
        """راه‌اندازی همه دستگاه‌ها"""
        print(f"🚀 Starting {self.max_devices} devices...")
        
        successful = 0
        failed = 0
        
        # راه‌اندازی به صورت batch برای جلوگیری از overload
        batch_size = 10
        
        for batch_start in range(0, self.max_devices, batch_size):
            batch_end = min(batch_start + batch_size, self.max_devices)
            batch_devices = range(batch_start + 1, batch_end + 1)
            
            print(f"📱 Starting batch {batch_start//batch_size + 1}: devices {batch_start + 1}-{batch_end}")
            
            with ThreadPoolExecutor(max_workers=batch_size) as executor:
                futures = []
                
                for device_id in batch_devices:
                    future = executor.submit(self.start_device, device_id)
                    futures.append((device_id, future))
                
                # جمع‌آوری نتایج
                for device_id, future in futures:
                    try:
                        if future.result(timeout=30):
                            successful += 1
                            print(f"✅ Device {device_id} started")
                        else:
                            failed += 1
                            print(f"❌ Device {device_id} failed")
                    except Exception as e:
                        failed += 1
                        print(f"❌ Device {device_id} error: {e}")
                    
                    # به‌روزرسانی progress
                    if progress_callback:
                        progress = ((successful + failed) / self.max_devices) * 100
                        progress_callback(progress, f"Device {device_id}")
            
            # استراحت بین batch ها
            if batch_end < self.max_devices:
                print("⏳ Waiting 2 seconds before next batch...")
                time.sleep(2)
        
        print(f"🎉 Startup complete: {successful} successful, {failed} failed")
        return successful
    
    def stop_all_devices(self, progress_callback=None):
        """توقف همه دستگاه‌ها"""
        print("🛑 Stopping all devices...")
        
        device_ids = list(self.running_devices.keys())
        stopped = 0
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = []
            
            for device_id in device_ids:
                future = executor.submit(self.stop_device, device_id)
                futures.append((device_id, future))
            
            # جمع‌آوری نتایج
            for device_id, future in futures:
                try:
                    future.result(timeout=10)
                    stopped += 1
                    print(f"✅ Device {device_id} stopped")
                    
                    # به‌روزرسانی progress
                    if progress_callback:
                        progress = (stopped / len(device_ids)) * 100
                        progress_callback(progress, f"Stopping device {device_id}")
                        
                except Exception as e:
                    print(f"❌ Device {device_id} stop error: {e}")
        
        print(f"🎉 {stopped}/{len(device_ids)} devices stopped")
        return stopped
    
    def install_apk_on_all(self, apk_path, progress_callback=None):
        """نصب APK روی همه دستگاه‌ها"""
        if not os.path.exists(apk_path):
            print(f"❌ APK file not found: {apk_path}")
            return 0
        
        print(f"📦 Installing APK on {len(self.running_devices)} devices...")
        
        successful = 0
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = []
            
            for device_id, device in self.running_devices.items():
                future = executor.submit(device.install_apk, apk_path)
                futures.append((device_id, future))
            
            # جمع‌آوری نتایج
            for device_id, future in futures:
                try:
                    if future.result(timeout=60):
                        successful += 1
                        print(f"✅ APK installed on device {device_id}")
                        
                        # به‌روزرسانی progress
                        if progress_callback:
                            progress = (successful / len(self.running_devices)) * 100
                            progress_callback(progress, f"Installing on device {device_id}")
                    else:
                        print(f"❌ APK installation failed on device {device_id}")
                except Exception as e:
                    print(f"❌ Device {device_id} install error: {e}")
        
        self.stats['installed_apps'] = successful
        print(f"🎉 APK installed on {successful}/{len(self.running_devices)} devices")
        return successful
    
    def launch_kepithor_on_all(self, progress_callback=None):
        """اجرای Kepithor روی همه دستگاه‌ها"""
        print(f"🚀 Launching Kepithor on {len(self.running_devices)} devices...")
        
        successful = 0
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = []
            
            for device_id, device in self.running_devices.items():
                future = executor.submit(device.launch_kepithor)
                futures.append((device_id, future))
            
            # جمع‌آوری نتایج
            for device_id, future in futures:
                try:
                    if future.result(timeout=30):
                        successful += 1
                        print(f"✅ Kepithor launched on device {device_id}")
                        
                        # به‌روزرسانی progress
                        if progress_callback:
                            progress = (successful / len(self.running_devices)) * 100
                            progress_callback(progress, f"Launching on device {device_id}")
                    else:
                        print(f"❌ Kepithor launch failed on device {device_id}")
                except Exception as e:
                    print(f"❌ Device {device_id} launch error: {e}")
        
        self.stats['active_kepithor'] = successful
        print(f"🎉 Kepithor launched on {successful}/{len(self.running_devices)} devices")
        return successful
    
    def get_system_stats(self):
        """دریافت آمار سیستم"""
        try:
            # آمار CPU و RAM
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            # شمارش دستگاه‌های فعال Kepithor
            active_kepithor = 0
            for device in self.running_devices.values():
                if device.current_app == 'com.KepithorStudios.KKTFaucet':
                    active_kepithor += 1
            
            self.stats.update({
                'running_devices': len(self.running_devices),
                'active_kepithor': active_kepithor,
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'memory_used_gb': memory.used / (1024**3)
            })
            
            return self.stats
            
        except Exception as e:
            print(f"❌ Error getting system stats: {e}")
            return self.stats
    
    def get_devices_info(self):
        """دریافت اطلاعات تمام دستگاه‌ها"""
        devices_info = []
        
        for device_id, device in self.devices.items():
            info = device.get_device_info()
            devices_info.append(info)
        
        return devices_info
    
    def print_status(self):
        """چاپ وضعیت فعلی"""
        stats = self.get_system_stats()
        
        print("\n" + "="*80)
        print("📊 ANDROID EMULATOR FARM STATUS")
        print("="*80)
        print(f"📱 Total Devices: {self.stats['total_devices']}")
        print(f"🟢 Running Devices: {stats['running_devices']}")
        print(f"📦 Devices with APK: {self.stats['installed_apps']}")
        print(f"💰 Active Kepithor: {stats['active_kepithor']}")
        print(f"💾 Memory Usage: {stats['memory_usage']:.1f}% ({stats['memory_used_gb']:.1f} GB)")
        print(f"🔥 CPU Usage: {stats['cpu_usage']:.1f}%")
        print(f"💽 Available Memory: {stats['memory_available_gb']:.1f} GB")
        print("="*80)
        
        # نمایش IP های دستگاه‌ها
        if self.running_devices:
            print("\n🌐 Device IP Addresses:")
            for device_id, device in list(self.running_devices.items())[:10]:  # نمایش 10 تای اول
                print(f"  Device {device_id}: {device.ip_address}")
            
            if len(self.running_devices) > 10:
                print(f"  ... and {len(self.running_devices) - 10} more devices")

def main():
    """تابع اصلی برای تست"""
    print("🏭 Android Emulator Farm")
    print("=" * 50)
    
    # ایجاد مزرعه
    farm = EmulatorFarm(max_devices=5)  # تست با 5 دستگاه
    
    try:
        # راه‌اندازی دستگاه‌ها
        print("1. Starting devices...")
        successful = farm.start_all_devices()
        
        if successful > 0:
            # نمایش آمار
            farm.print_status()
            
            # نصب APK (اگر موجود باشد)
            apk_path = "../extracted_xapk/com.KepithorStudios.KKTFaucet.apk"
            if os.path.exists(apk_path):
                print("\n2. Installing APK...")
                farm.install_apk_on_all(apk_path)
                
                print("\n3. Launching Kepithor...")
                farm.launch_kepithor_on_all()
            
            # نمایش آمار نهایی
            print("\n4. Final status:")
            farm.print_status()
            
            # نگه داشتن برای مشاهده
            input("\nPress Enter to shutdown all devices...")
        
        # توقف همه دستگاه‌ها
        farm.stop_all_devices()
        
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        farm.stop_all_devices()

if __name__ == "__main__":
    main()
