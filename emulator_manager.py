#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Android Emulator Manager - 80 Concurrent Devices
مدیریت 80 شبیه‌ساز اندروید همزمان
"""

import os
import sys
import json
import time
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import psutil
import tkinter as tk
from tkinter import ttk, messagebox, filedialog

from android_emulator import AndroidDevice

class EmulatorManager:
    """مدیریت چندین شبیه‌ساز همزمان"""
    
    def __init__(self, max_devices=80):
        """راه‌اندازی مدیر"""
        self.max_devices = max_devices
        self.devices = {}
        self.running_devices = {}
        self.base_port = 5555
        
        # آمار
        self.stats = {
            'total_devices': 0,
            'running_devices': 0,
            'installed_apps': 0,
            'memory_usage': 0,
            'cpu_usage': 0
        }
        
        # ایجاد فولدر اصلی
        self.base_dir = Path("android_farm")
        self.base_dir.mkdir(exist_ok=True)
        
        print(f"🏭 Emulator Manager initialized for {max_devices} devices")
    
    def create_device(self, device_id):
        """ایجاد دستگاه جدید"""
        try:
            port = self.base_port + device_id
            device = AndroidDevice(device_id, port)
            self.devices[device_id] = device
            self.stats['total_devices'] += 1
            
            print(f"📱 Device {device_id} created on port {port}")
            return device
            
        except Exception as e:
            print(f"❌ Error creating device {device_id}: {e}")
            return None
    
    def start_device(self, device_id):
        """راه‌اندازی دستگاه"""
        try:
            if device_id not in self.devices:
                device = self.create_device(device_id)
                if not device:
                    return False
            
            device = self.devices[device_id]
            
            if device.start_device():
                self.running_devices[device_id] = device
                self.stats['running_devices'] += 1
                return True
            else:
                return False
                
        except Exception as e:
            print(f"❌ Error starting device {device_id}: {e}")
            return False
    
    def stop_device(self, device_id):
        """توقف دستگاه"""
        try:
            if device_id in self.running_devices:
                device = self.running_devices[device_id]
                device.shutdown()
                del self.running_devices[device_id]
                self.stats['running_devices'] -= 1
                return True
            else:
                print(f"⚠️ Device {device_id} not running")
                return False
                
        except Exception as e:
            print(f"❌ Error stopping device {device_id}: {e}")
            return False
    
    def start_all_devices(self):
        """راه‌اندازی همه دستگاه‌ها"""
        print(f"🚀 Starting {self.max_devices} devices...")
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = []
            
            for device_id in range(1, self.max_devices + 1):
                future = executor.submit(self.start_device, device_id)
                futures.append((device_id, future))
            
            # جمع‌آوری نتایج
            successful = 0
            for device_id, future in futures:
                try:
                    if future.result(timeout=30):
                        successful += 1
                        print(f"✅ Device {device_id} started")
                    else:
                        print(f"❌ Device {device_id} failed to start")
                except Exception as e:
                    print(f"❌ Device {device_id} error: {e}")
        
        print(f"🎉 {successful}/{self.max_devices} devices started successfully")
        return successful
    
    def stop_all_devices(self):
        """توقف همه دستگاه‌ها"""
        print("🛑 Stopping all devices...")
        
        device_ids = list(self.running_devices.keys())
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = []
            
            for device_id in device_ids:
                future = executor.submit(self.stop_device, device_id)
                futures.append((device_id, future))
            
            # جمع‌آوری نتایج
            for device_id, future in futures:
                try:
                    future.result(timeout=10)
                    print(f"✅ Device {device_id} stopped")
                except Exception as e:
                    print(f"❌ Device {device_id} stop error: {e}")
        
        print("🎉 All devices stopped")
    
    def install_apk_on_all(self, apk_path):
        """نصب APK روی همه دستگاه‌ها"""
        if not os.path.exists(apk_path):
            print(f"❌ APK file not found: {apk_path}")
            return False
        
        print(f"📦 Installing APK on {len(self.running_devices)} devices...")
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = []
            
            for device_id, device in self.running_devices.items():
                future = executor.submit(device.install_apk, apk_path)
                futures.append((device_id, future))
            
            # جمع‌آوری نتایج
            successful = 0
            for device_id, future in futures:
                try:
                    if future.result(timeout=60):
                        successful += 1
                        print(f"✅ APK installed on device {device_id}")
                    else:
                        print(f"❌ APK installation failed on device {device_id}")
                except Exception as e:
                    print(f"❌ Device {device_id} install error: {e}")
        
        print(f"🎉 APK installed on {successful}/{len(self.running_devices)} devices")
        return successful
    
    def launch_app_on_all(self, package_name):
        """اجرای برنامه روی همه دستگاه‌ها"""
        print(f"🚀 Launching {package_name} on {len(self.running_devices)} devices...")
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = []
            
            for device_id, device in self.running_devices.items():
                future = executor.submit(device.launch_app, package_name)
                futures.append((device_id, future))
            
            # جمع‌آوری نتایج
            successful = 0
            for device_id, future in futures:
                try:
                    if future.result(timeout=30):
                        successful += 1
                        print(f"✅ App launched on device {device_id}")
                    else:
                        print(f"❌ App launch failed on device {device_id}")
                except Exception as e:
                    print(f"❌ Device {device_id} launch error: {e}")
        
        print(f"🎉 App launched on {successful}/{len(self.running_devices)} devices")
        return successful
    
    def get_system_stats(self):
        """دریافت آمار سیستم"""
        try:
            # آمار CPU و RAM
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            self.stats.update({
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'running_devices': len(self.running_devices)
            })
            
            return self.stats
            
        except Exception as e:
            print(f"❌ Error getting system stats: {e}")
            return self.stats
    
    def print_status(self):
        """چاپ وضعیت فعلی"""
        stats = self.get_system_stats()
        
        print("\n" + "="*60)
        print("📊 EMULATOR FARM STATUS")
        print("="*60)
        print(f"📱 Total Devices: {stats['total_devices']}")
        print(f"🟢 Running Devices: {stats['running_devices']}")
        print(f"💾 Memory Usage: {stats['memory_usage']:.1f}%")
        print(f"🔥 CPU Usage: {stats['cpu_usage']:.1f}%")
        print(f"💽 Available Memory: {stats.get('memory_available_gb', 0):.1f} GB")
        print("="*60)

class EmulatorManagerGUI:
    """رابط گرافیکی برای مدیریت شبیه‌سازها"""
    
    def __init__(self):
        """راه‌اندازی رابط گرافیکی"""
        self.manager = EmulatorManager(max_devices=80)
        self.setup_gui()
        
    def setup_gui(self):
        """ایجاد رابط گرافیکی"""
        self.root = tk.Tk()
        self.root.title("🤖 Android Emulator Farm Manager")
        self.root.geometry("800x600")
        self.root.configure(bg="#f0f0f0")
        
        # فریم اصلی
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # عنوان
        title_label = tk.Label(
            main_frame,
            text="🤖 مدیریت مزرعه شبیه‌ساز اندروید",
            font=("Arial", 16, "bold"),
            bg="#f0f0f0"
        )
        title_label.grid(row=0, column=0, columnspan=3, pady=10)
        
        # دکمه‌های کنترل
        control_frame = ttk.LabelFrame(main_frame, text="کنترل دستگاه‌ها", padding="10")
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # دکمه راه‌اندازی همه
        self.start_all_btn = tk.Button(
            control_frame,
            text="🚀 راه‌اندازی همه دستگاه‌ها",
            command=self.start_all_devices,
            font=("Arial", 10),
            bg="#4CAF50",
            fg="white",
            width=25
        )
        self.start_all_btn.grid(row=0, column=0, padx=5, pady=5)
        
        # دکمه توقف همه
        self.stop_all_btn = tk.Button(
            control_frame,
            text="🛑 توقف همه دستگاه‌ها",
            command=self.stop_all_devices,
            font=("Arial", 10),
            bg="#f44336",
            fg="white",
            width=25
        )
        self.stop_all_btn.grid(row=0, column=1, padx=5, pady=5)
        
        # دکمه نصب APK
        self.install_apk_btn = tk.Button(
            control_frame,
            text="📦 نصب APK",
            command=self.install_apk,
            font=("Arial", 10),
            bg="#FF9800",
            fg="white",
            width=25
        )
        self.install_apk_btn.grid(row=1, column=0, padx=5, pady=5)
        
        # دکمه اجرای برنامه
        self.launch_app_btn = tk.Button(
            control_frame,
            text="🚀 اجرای Kepithor",
            command=self.launch_kepithor,
            font=("Arial", 10),
            bg="#2196F3",
            fg="white",
            width=25
        )
        self.launch_app_btn.grid(row=1, column=1, padx=5, pady=5)
        
        # آمار سیستم
        stats_frame = ttk.LabelFrame(main_frame, text="آمار سیستم", padding="10")
        stats_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.stats_text = tk.Text(stats_frame, height=8, width=80)
        self.stats_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # نوار پیمایش
        scrollbar = ttk.Scrollbar(stats_frame, orient="vertical", command=self.stats_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.stats_text.configure(yscrollcommand=scrollbar.set)
        
        # لیست دستگاه‌ها
        devices_frame = ttk.LabelFrame(main_frame, text="لیست دستگاه‌ها", padding="10")
        devices_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # جدول دستگاه‌ها
        columns = ("ID", "Port", "Status", "Apps", "Memory")
        self.devices_tree = ttk.Treeview(devices_frame, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.devices_tree.heading(col, text=col)
            self.devices_tree.column(col, width=100)
        
        self.devices_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # نوار پیمایش جدول
        tree_scrollbar = ttk.Scrollbar(devices_frame, orient="vertical", command=self.devices_tree.yview)
        tree_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.devices_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        # به‌روزرسانی خودکار
        self.update_stats()
        
        # تنظیم grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        devices_frame.columnconfigure(0, weight=1)
        devices_frame.rowconfigure(0, weight=1)
    
    def start_all_devices(self):
        """راه‌اندازی همه دستگاه‌ها"""
        self.start_all_btn.config(state="disabled", text="در حال راه‌اندازی...")
        
        def start_thread():
            try:
                successful = self.manager.start_all_devices()
                self.root.after(0, lambda: self.update_button_state(
                    self.start_all_btn, "🚀 راه‌اندازی همه دستگاه‌ها", "normal"
                ))
                self.root.after(0, lambda: messagebox.showinfo(
                    "اطلاعات", f"{successful} دستگاه با موفقیت راه‌اندازی شد"
                ))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در راه‌اندازی: {e}"))
                self.root.after(0, lambda: self.update_button_state(
                    self.start_all_btn, "🚀 راه‌اندازی همه دستگاه‌ها", "normal"
                ))
        
        threading.Thread(target=start_thread, daemon=True).start()
    
    def stop_all_devices(self):
        """توقف همه دستگاه‌ها"""
        self.stop_all_btn.config(state="disabled", text="در حال توقف...")
        
        def stop_thread():
            try:
                self.manager.stop_all_devices()
                self.root.after(0, lambda: self.update_button_state(
                    self.stop_all_btn, "🛑 توقف همه دستگاه‌ها", "normal"
                ))
                self.root.after(0, lambda: messagebox.showinfo("اطلاعات", "همه دستگاه‌ها متوقف شدند"))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در توقف: {e}"))
                self.root.after(0, lambda: self.update_button_state(
                    self.stop_all_btn, "🛑 توقف همه دستگاه‌ها", "normal"
                ))
        
        threading.Thread(target=stop_thread, daemon=True).start()
    
    def install_apk(self):
        """نصب APK"""
        apk_path = filedialog.askopenfilename(
            title="انتخاب فایل APK",
            filetypes=[("APK files", "*.apk"), ("All files", "*.*")]
        )
        
        if apk_path:
            self.install_apk_btn.config(state="disabled", text="در حال نصب...")
            
            def install_thread():
                try:
                    successful = self.manager.install_apk_on_all(apk_path)
                    self.root.after(0, lambda: self.update_button_state(
                        self.install_apk_btn, "📦 نصب APK", "normal"
                    ))
                    self.root.after(0, lambda: messagebox.showinfo(
                        "اطلاعات", f"APK روی {successful} دستگاه نصب شد"
                    ))
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در نصب: {e}"))
                    self.root.after(0, lambda: self.update_button_state(
                        self.install_apk_btn, "📦 نصب APK", "normal"
                    ))
            
            threading.Thread(target=install_thread, daemon=True).start()
    
    def launch_kepithor(self):
        """اجرای Kepithor"""
        self.launch_app_btn.config(state="disabled", text="در حال اجرا...")
        
        def launch_thread():
            try:
                successful = self.manager.launch_app_on_all('com.KepithorStudios.KKTFaucet')
                self.root.after(0, lambda: self.update_button_state(
                    self.launch_app_btn, "🚀 اجرای Kepithor", "normal"
                ))
                self.root.after(0, lambda: messagebox.showinfo(
                    "اطلاعات", f"Kepithor روی {successful} دستگاه اجرا شد"
                ))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در اجرا: {e}"))
                self.root.after(0, lambda: self.update_button_state(
                    self.launch_app_btn, "🚀 اجرای Kepithor", "normal"
                ))
        
        threading.Thread(target=launch_thread, daemon=True).start()
    
    def update_button_state(self, button, text, state):
        """به‌روزرسانی وضعیت دکمه"""
        button.config(text=text, state=state)
    
    def update_stats(self):
        """به‌روزرسانی آمار"""
        try:
            stats = self.manager.get_system_stats()
            
            # به‌روزرسانی متن آمار
            stats_text = f"""
📊 آمار سیستم - {datetime.now().strftime('%H:%M:%S')}
{'='*50}
📱 کل دستگاه‌ها: {stats['total_devices']}
🟢 دستگاه‌های فعال: {stats['running_devices']}
💾 استفاده از حافظه: {stats['memory_usage']:.1f}%
🔥 استفاده از CPU: {stats['cpu_usage']:.1f}%
💽 حافظه آزاد: {stats.get('memory_available_gb', 0):.1f} GB

🎯 وضعیت دستگاه‌ها:
"""
            
            # اضافه کردن اطلاعات دستگاه‌ها
            for device_id, device in self.manager.running_devices.items():
                info = device.get_device_info()
                stats_text += f"  Device {device_id}: {info['specs']['model']} - {len(device.running_apps)} apps running\n"
            
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
            
            # به‌روزرسانی جدول دستگاه‌ها
            for item in self.devices_tree.get_children():
                self.devices_tree.delete(item)
            
            for device_id, device in self.manager.devices.items():
                info = device.get_device_info()
                status = "🟢 Running" if info['is_running'] else "🔴 Stopped"
                
                self.devices_tree.insert("", "end", values=(
                    device_id,
                    info['port'],
                    status,
                    info['installed_apps'],
                    f"{info['specs']['ram_mb']} MB"
                ))
            
        except Exception as e:
            print(f"❌ Error updating stats: {e}")
        
        # برنامه‌ریزی به‌روزرسانی بعدی
        self.root.after(5000, self.update_stats)  # هر 5 ثانیه
    
    def run(self):
        """اجرای رابط گرافیکی"""
        self.root.mainloop()

def main():
    """تابع اصلی"""
    print("🤖 Android Emulator Farm Manager")
    print("=" * 50)
    
    # اجرای رابط گرافیکی
    gui = EmulatorManagerGUI()
    gui.run()

if __name__ == "__main__":
    main()
