#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test Script for Android Emulator
تست سریع شبیه‌ساز اندروید
"""

import os
import time
from emulator_manager import EmulatorManager

def test_single_device():
    """تست یک دستگاه"""
    print("🧪 Testing Single Device")
    print("=" * 40)
    
    manager = EmulatorManager(max_devices=1)
    
    # راه‌اندازی دستگاه
    print("1. Starting device...")
    if manager.start_device(1):
        print("✅ Device started successfully")
    else:
        print("❌ Failed to start device")
        return
    
    # نصب APK
    apk_path = "extracted_xapk/com.KepithorStudios.KKTFaucet.apk"
    if os.path.exists(apk_path):
        print("2. Installing APK...")
        if manager.install_apk_on_all(apk_path):
            print("✅ APK installed successfully")
        else:
            print("❌ Failed to install APK")
    else:
        print("⚠️ APK file not found, skipping installation")
    
    # اجرای برنامه
    print("3. Launching Kepithor...")
    if manager.launch_app_on_all('com.KepithorStudios.KKTFaucet'):
        print("✅ App launched successfully")
    else:
        print("❌ Failed to launch app")
    
    # نمایش آمار
    print("4. System stats:")
    manager.print_status()
    
    # توقف
    print("5. Stopping device...")
    manager.stop_all_devices()
    print("✅ Test completed")

def test_multiple_devices():
    """تست چندین دستگاه"""
    print("🧪 Testing Multiple Devices (5 devices)")
    print("=" * 40)
    
    manager = EmulatorManager(max_devices=5)
    
    # راه‌اندازی دستگاه‌ها
    print("1. Starting 5 devices...")
    successful = manager.start_all_devices()
    print(f"✅ {successful}/5 devices started")
    
    # نصب APK
    apk_path = "extracted_xapk/com.KepithorStudios.KKTFaucet.apk"
    if os.path.exists(apk_path):
        print("2. Installing APK on all devices...")
        successful = manager.install_apk_on_all(apk_path)
        print(f"✅ APK installed on {successful} devices")
    
    # اجرای برنامه
    print("3. Launching Kepithor on all devices...")
    successful = manager.launch_app_on_all('com.KepithorStudios.KKTFaucet')
    print(f"✅ App launched on {successful} devices")
    
    # نمایش آمار
    print("4. System stats:")
    manager.print_status()
    
    # نگه داشتن برای مشاهده
    print("5. Running for 30 seconds...")
    for i in range(6):
        time.sleep(5)
        print(f"   {(i+1)*5}s - {len(manager.running_devices)} devices running")
        manager.print_status()
    
    # توقف
    print("6. Stopping all devices...")
    manager.stop_all_devices()
    print("✅ Test completed")

def test_performance():
    """تست عملکرد سیستم"""
    print("🧪 Performance Test")
    print("=" * 40)
    
    import psutil
    
    # آمار قبل از شروع
    print("System stats before test:")
    print(f"  CPU: {psutil.cpu_percent()}%")
    print(f"  Memory: {psutil.virtual_memory().percent}%")
    print(f"  Available Memory: {psutil.virtual_memory().available / (1024**3):.1f} GB")
    
    # تست با تعداد مختلف دستگاه
    device_counts = [1, 5, 10, 20]
    
    for count in device_counts:
        print(f"\n--- Testing {count} devices ---")
        
        manager = EmulatorManager(max_devices=count)
        
        start_time = time.time()
        successful = manager.start_all_devices()
        startup_time = time.time() - start_time
        
        print(f"  Startup time: {startup_time:.2f}s")
        print(f"  Success rate: {successful}/{count} ({successful/count*100:.1f}%)")
        
        # آمار سیستم
        stats = manager.get_system_stats()
        print(f"  CPU: {stats['cpu_usage']:.1f}%")
        print(f"  Memory: {stats['memory_usage']:.1f}%")
        
        # توقف
        manager.stop_all_devices()
        time.sleep(2)  # استراحت بین تست‌ها
    
    print("\n✅ Performance test completed")

def main():
    """منوی اصلی"""
    print("🤖 Android Emulator Test Suite")
    print("=" * 50)
    print("1. Test single device")
    print("2. Test multiple devices (5)")
    print("3. Performance test")
    print("4. Launch GUI manager")
    print("5. Exit")
    
    while True:
        try:
            choice = input("\nSelect option (1-5): ").strip()
            
            if choice == "1":
                test_single_device()
            elif choice == "2":
                test_multiple_devices()
            elif choice == "3":
                test_performance()
            elif choice == "4":
                print("🚀 Launching GUI manager...")
                from emulator_manager import EmulatorManagerGUI
                gui = EmulatorManagerGUI()
                gui.run()
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice!")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
