;(function(omidGlobal) {
  'use strict';var n;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}function p(a){var b='undefined'!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):{next:aa(a)}}function q(a){if(!(a instanceof Array)){a=p(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}var ba='function'==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},ca;
if('function'==typeof Object.setPrototypeOf)ca=Object.setPrototypeOf;else{var da;a:{var ea={S:!0},fa={};try{fa.__proto__=ea;da=fa.S;break a}catch(a){}da=!1}ca=da?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+' is not extensible');return a}:null}var ha=ca;
function r(a,b){a.prototype=ba(b.prototype);a.prototype.constructor=a;if(ha)ha(a,b);else for(var c in b)if('prototype'!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c]}function ia(a){a=['object'==typeof window&&window,'object'==typeof self&&self,'object'==typeof global&&global,a];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}return globalThis}
var t=ia(this),v='function'==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){a!=Array.prototype&&a!=Object.prototype&&(a[b]=c.value)};function x(a,b){if(b){var c=t;a=a.split('.');for(var d=0;d<a.length-1;d++){var e=a[d];e in c||(c[e]={});c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&v(c,a,{configurable:!0,writable:!0,value:b})}}x('globalThis',function(a){return a||t});function y(a,b){return Object.prototype.hasOwnProperty.call(a,b)}
var ja='function'==typeof Object.assign?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)y(d,e)&&(a[e]=d[e])}return a};x('Object.assign',function(a){return a||ja});function ka(){ka=function(){};t.Symbol||(t.Symbol=la)}function ma(a,b){this.a=a;v(this,'description',{configurable:!0,writable:!0,value:b})}ma.prototype.toString=function(){return this.a};
var la=function(){function a(c){if(this instanceof a)throw new TypeError('Symbol is not a constructor');return new ma('jscomp_symbol_'+(c||'')+'_'+b++,c)}var b=0;return a}();function z(){ka();var a=t.Symbol.iterator;a||(a=t.Symbol.iterator=t.Symbol('Symbol.iterator'));'function'!=typeof Array.prototype[a]&&v(Array.prototype,a,{configurable:!0,writable:!0,value:function(){return na(aa(this))}});z=function(){}}function na(a){z();a={next:a};a[t.Symbol.iterator]=function(){return this};return a}
x('WeakMap',function(a){function b(h){this.a=(g+=Math.random()+1).toString();if(h){h=p(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}}function c(){}function d(h){var k=typeof h;return'object'===k&&null!==h||'function'===k}function e(h){if(!y(h,l)){var k=new c;v(h,l,{value:k})}}function f(h){var k=Object[h];k&&(Object[h]=function(m){if(m instanceof c)return m;e(m);return k(m)})}if(function(){if(!a||!Object.seal)return!1;try{var h=Object.seal({}),k=Object.seal({}),m=new a([[h,2],[k,
3]]);if(2!=m.get(h)||3!=m.get(k))return!1;m.delete(h);m.set(k,4);return!m.has(h)&&4==m.get(k)}catch(u){return!1}}())return a;var l='$jscomp_hidden_'+Math.random();f('freeze');f('preventExtensions');f('seal');var g=0;b.prototype.set=function(h,k){if(!d(h))throw Error('Invalid WeakMap key');e(h);if(!y(h,l))throw Error('WeakMap key fail: '+h);h[l][this.a]=k;return this};b.prototype.get=function(h){return d(h)&&y(h,l)?h[l][this.a]:void 0};b.prototype.has=function(h){return d(h)&&y(h,l)&&y(h[l],this.a)};
b.prototype.delete=function(h){return d(h)&&y(h,l)&&y(h[l],this.a)?delete h[l][this.a]:!1};return b});
x('Map',function(a){function b(){var g={};return g.A=g.next=g.head=g}function c(g,h){var k=g.a;return na(function(){if(k){for(;k.head!=g.a;)k=k.A;for(;k.next!=k.head;)return k=k.next,{done:!1,value:h(k)};k=null}return{done:!0,value:void 0}})}function d(g,h){var k=h&&typeof h;'object'==k||'function'==k?f.has(h)?k=f.get(h):(k=''+ ++l,f.set(h,k)):k='p_'+h;var m=g.b[k];if(m&&y(g.b,k))for(g=0;g<m.length;g++){var u=m[g];if(h!==h&&u.key!==u.key||h===u.key)return{id:k,list:m,index:g,s:u}}return{id:k,list:m,
index:-1,s:void 0}}function e(g){this.b={};this.a=b();this.size=0;if(g){g=p(g);for(var h;!(h=g.next()).done;)h=h.value,this.set(h[0],h[1])}}if(function(){if(!a||'function'!=typeof a||!a.prototype.entries||'function'!=typeof Object.seal)return!1;try{var g=Object.seal({x:4}),h=new a(p([[g,'s']]));if('s'!=h.get(g)||1!=h.size||h.get({x:4})||h.set({x:4},'t')!=h||2!=h.size)return!1;var k=h.entries(),m=k.next();if(m.done||m.value[0]!=g||'s'!=m.value[1])return!1;m=k.next();return m.done||4!=m.value[0].x||
't'!=m.value[1]||!k.next().done?!1:!0}catch(u){return!1}}())return a;z();var f=new WeakMap;e.prototype.set=function(g,h){g=0===g?0:g;var k=d(this,g);k.list||(k.list=this.b[k.id]=[]);k.s?k.s.value=h:(k.s={next:this.a,A:this.a.A,head:this.a,key:g,value:h},k.list.push(k.s),this.a.A.next=k.s,this.a.A=k.s,this.size++);return this};e.prototype.delete=function(g){g=d(this,g);return g.s&&g.list?(g.list.splice(g.index,1),g.list.length||delete this.b[g.id],g.s.A.next=g.s.next,g.s.next.A=g.s.A,g.s.head=null,
this.size--,!0):!1};e.prototype.clear=function(){this.b={};this.a=this.a.A=b();this.size=0};e.prototype.has=function(g){return!!d(this,g).s};e.prototype.get=function(g){return(g=d(this,g).s)&&g.value};e.prototype.entries=function(){return c(this,function(g){return[g.key,g.value]})};e.prototype.keys=function(){return c(this,function(g){return g.key})};e.prototype.values=function(){return c(this,function(g){return g.value})};e.prototype.forEach=function(g,h){for(var k=this.entries(),m;!(m=k.next()).done;)m=
m.value,g.call(h,m[1],m[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var l=0;return e});x('Object.values',function(a){return a?a:function(b){var c=[],d;for(d in b)y(b,d)&&c.push(b[d]);return c}});
x('Set',function(a){function b(c){this.a=new Map;if(c){c=p(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.a.size}if(function(){if(!a||'function'!=typeof a||!a.prototype.entries||'function'!=typeof Object.seal)return!1;try{var c=Object.seal({x:4}),d=new a(p([c]));if(!d.has(c)||1!=d.size||d.add(c)!=d||1!=d.size||d.add({x:4})!=d||2!=d.size)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||4!=f.value[0].x||
f.value[1]!=f.value[0]?!1:e.next().done}catch(l){return!1}}())return a;z();b.prototype.add=function(c){c=0===c?0:c;this.a.set(c,c);this.size=this.a.size;return this};b.prototype.delete=function(c){c=this.a.delete(c);this.size=this.a.size;return c};b.prototype.clear=function(){this.a.clear();this.size=0};b.prototype.has=function(c){return this.a.has(c)};b.prototype.entries=function(){return this.a.entries()};b.prototype.values=function(){return this.a.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.a.forEach(function(f){return c.call(d,f,f,e)})};return b});x('Object.is',function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});x('Array.prototype.includes',function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
x('Object.entries',function(a){return a?a:function(b){var c=[],d;for(d in b)y(b,d)&&c.push([d,b[d]]);return c}});
var A={qa:'loaded',xa:'start',ka:'firstQuartile',sa:'midpoint',ya:'thirdQuartile',ia:'complete',ta:'pause',va:'resume',ha:'bufferStart',ga:'bufferFinish',wa:'skipped',Aa:'volumeChange',ua:'playerStateChange',da:'adUserInteraction'},oa={na:'generic',za:'video',ra:'media'},pa={R:'native',oa:'html',P:'javascript'},ra={R:'native',P:'javascript',NONE:'none'},sa={ma:'full',ja:'domain',pa:'limited'},ta={fa:'backgrounded',la:'foregrounded'},ua={ea:'app',Ba:'web'};function B(a,b){this.x=null!=a.x?a.x:a.left;this.y=null!=a.y?a.y:a.top;this.width=a.width;this.height=a.height;this.endX=this.x+this.width;this.endY=this.y+this.height;this.adSessionId=a.adSessionId||void 0;this.isFriendlyObstructionFor=a.isFriendlyObstructionFor||[];this.b=a.friendlyObstructionClass||void 0;this.c=a.friendlyObstructionPurpose||void 0;this.f=a.friendlyObstructionReason||void 0;this.clipsToBounds=void 0!==a.clipsToBounds?!0===a.clipsToBounds:!0;this.g=void 0!==a.hasWindowFocus?!0===
a.hasWindowFocus:!0;this.notVisibleReason=a.notVisibleReason||void 0;this.childViews=a.childViews||[];this.isCreative=a.isCreative||!1;this.a=b}function va(a){var b={};return b.width=a.width,b.height=a.height,b}function C(a){var b={};return Object.assign({},va(a),(b.x=a.x,b.y=a.y,b))}function wa(a){var b=C(a),c={};return Object.assign({},b,(c.endX=a.endX,c.endY=a.endY,c))}function xa(a,b,c){a.x+=b;a.y+=c;a.endX+=b;a.endY+=c}
B.prototype.J=function(a){if(null==a)return!1;a=C(a);var b=a.y,c=a.width,d=a.height;return this.x===a.x&&this.y===b&&this.width===c&&this.height===d};function ya(a){return a.width*a.height}function za(a){return 0===a.width||0===a.height};function Aa(a,b){var c=0;if(0<b.length){var d=Ba(a,b);a=d.ba;d=d.ca;for(var e=0;e<a.length-1;e++)for(var f=(a[e]+(a[e]+1))/2,l=a[e+1]-a[e],g=0;g<d.length-1;g++){for(var h=(d[g]+(d[g]+1))/2,k=d[g+1]-d[g],m=!1,u=0;u<b.length;u++){var w=C(b[u]);if(w.x<f&&w.x+w.width>f&&w.y<h&&w.y+w.height>h){m=!0;break}}m&&(c+=Math.round(l)*Math.round(k))}}return c}
function Ba(a,b){a=C(a);for(var c=[],d=[],e=0;e<b.length;e++){var f=C(b[e]);f=Ca(a,f);Da(c,f.x);Da(c,f.endX);Da(d,f.y);Da(d,f.endY)}c=c.sort(function(l,g){return l-g});d=d.sort(function(l,g){return l-g});return{ba:c,ca:d}}function Ca(a,b){return{x:Math.max(a.x,b.x),y:Math.max(a.y,b.y),endX:Math.min(a.x+a.width,b.x+b.width),endY:Math.min(a.y+a.height,b.y+b.height)}}function Da(a,b){-1===a.indexOf(b)&&a.push(b)};function Ea(){this.b=this.a=this.v=this.l=this.g=this.j=void 0;this.m=0;this.h=[];this.o=[];this.u=0;this.i=[];this.c=[];this.f=[]}Ea.prototype.J=function(a){return null==a?!1:JSON.stringify(Fa(this))===JSON.stringify(Fa(a))};
function Fa(a){var b=[],c=[],d={viewport:a.j,adView:{percentageInView:a.m,pixelsInView:a.u,reasons:a.f},declaredFriendlyObstructions:a.h.length};if(void 0!==a.a){d.adView.geometry=C(a.a);d.adView.geometry.pixels=ya(a.a);d.adView.onScreenGeometry=C(a.b);d.adView.onScreenGeometry.pixels=Math.max(0,ya(a.b)-Aa(a.b,a.c));for(var e=0;e<a.c.length;e++)b.push(C(a.c[e]));for(e=0;e<a.o.length;e++){var f=a.o[e],l=f,g={};l.b&&(g.obstructionClass=l.b);l.c&&(g.obstructionPurpose=l.c);l.f&&(g.obstructionReason=
l.f);f=Ca(a.a,f);c.push(Object.assign({},{x:f.x,y:f.y,width:f.endX-f.x,height:f.endY-f.y},g))}d.adView.onScreenGeometry.obstructions=b;d.adView.onScreenGeometry.friendlyObstructions=c;void 0!==a.l&&void 0!==a.v&&(d.adView.containerGeometry=C(a.l),d.adView.onScreenContainerGeometry=C(a.v),d.adView.measuringElement=!0)}return d}function Ga(a,b){b=va(b);a.j={};a.j.width=b.width;a.j.height=b.height;a.g={};a.g.x=0;a.g.y=0;a.g.width=b.width;a.g.height=b.height;a.g.endX=b.width;a.g.endY=b.height}
function Ha(){return{x:0,y:0,endX:0,endY:0,width:0,height:0}}function Ia(a,b){var c={};c.x=Math.max(a.x,b.x);c.y=Math.max(a.y,b.y);c.endX=Math.min(a.endX,b.endX);c.endY=Math.min(a.endY,b.endY);c.width=Math.max(0,c.endX-c.x);c.height=Math.max(0,c.endY-c.y);return c}function Ja(a,b){return.01<b.width-a.width||.01<b.height-a.height}function Ka(a){if(-1!==a.f.indexOf('backgrounded'))a.m=0,a.u=0;else{var b=ya(a.a);if(0!==b){var c=Math.max(0,ya(a.b)-Aa(a.b,a.c));a.m=Math.round(c/b*100);a.u=c}}}
function La(a,b){if(za(b)||!a.b)b=!1;else{var c=wa(a.b),d=c.y,e=c.endX;a=c.endY;var f=b.endX;c=c.x;(f=f<c||.01>Math.abs(f-c))||(f=b.x,f=f>e||.01>Math.abs(f-e));(e=f)||(e=b.endY,e=e<d||.01>Math.abs(e-d));(d=e)||(b=b.y,d=b>a||.01>Math.abs(b-a));b=!d}return b}function D(a,b){for(var c=!1,d=0;d<a.f.length;d++)a.f[d]===b&&(c=!0);c||a.f.push(b)};function Ma(a,b,c,d,e){var f=new Ea;b=new B(b,!1);Ga(f,b);Na(a,b,f,d);if(!e)return f.f=['unmeasurable'],f.j=void 0,f.m=0,f.c=[],f.a&&(a=f.a,c={},a=new B((c.x=0,c.y=0,c.width=a.width,c.height=a.height,c),a.a),f.a=a),f.b=Ha(),f;if('backgrounded'===c)D(f,'backgrounded');else if(void 0!==f.a){for(a=0;a<f.h.length;a++)La(f,f.h[a])&&f.o.push(f.h[a]);for(a=0;a<f.i.length;a++){if(c=La(f,f.i[a])){a:{c=f.i[a];for(d=0;d<f.c.length;d++)if(f.c[d].J(c)){c=!0;break a}c=!1}c=!c}c&&(D(f,'obstructed'),f.c.push(f.i[a]))}Ka(f)}else D(f,
'notFound');return f}
function Na(a,b,c,d){var e=b.isCreative?!0:b.adSessionId===d;if(e){c.a=b;var f=wa(c.a);a=Ia(c.g,f);var l=c.a;'notAttached'===l.notVisibleReason||'noWindowFocus'===l.notVisibleReason||'noAdView'===l.notVisibleReason?(D(c,'notFound'),c.b=new B(Ha(),!1)):(l=c.a,'viewInvisible'===l.notVisibleReason||'viewGone'===l.notVisibleReason||'viewNotVisible'===l.notVisibleReason||'viewAlphaZero'===l.notVisibleReason||'viewHidden'===l.notVisibleReason||void 0!==c.a.notVisibleReason||za(c.a)?(D(c,'hidden'),c.b=new B(Ha(),
!1)):(c.a.g||(D(c,'backgrounded'),D(c,'noWindowFocus')),Ja(a,f)&&D(c,'clipped'),c.b=new B(a,!1)))}else if(f=!0,b.a&&(f=-1!==b.isFriendlyObstructionFor.indexOf(d)?!1:!1===b.clipsToBounds),f){l=b.childViews;for(var g=0;g<l.length;g++)f=void 0!==c.a,Na(a,new B(l[g],f),c,d)}e||void 0===c.a||(b.a?-1!==b.isFriendlyObstructionFor.indexOf(d)?c.h.push(b):c.i.push(b):(e=wa(b),d=wa(c.b),C(c.b),!za(c.b)&&b.clipsToBounds&&(b=Ia(d,e),Ja(b,d)&&(D(c,'clipped'),c.b=new B(b,!1)))))};function Oa(a,b){this.y=this.x=0;this.width=a;this.height=b};function Pa(){return{apiVersion:'1.0',accessMode:'limited',environment:'app',omidJsInfo:{omidImplementer:'omsdk',serviceVersion:'1.3.26-iab3222'}}}function Qa(){this.adSessionId=null;this.c=Pa();this.o=null;this.m='foregrounded';this.l=this.i='none';this.j=this.g=this.f=this.h=this.a=this.b=this.B=this.u=null;this.C=!0;this.v=new Map}var G;function H(){G||(G=new Qa);return G};var I=function(){if('undefined'!==typeof omidGlobal&&omidGlobal)return omidGlobal;if('undefined'!==typeof global&&global)return global;if('undefined'!==typeof window&&window)return window;if('undefined'!==typeof globalThis&&globalThis)return globalThis;var a=Function('return this')();if(a)return a;throw Error('Could not determine global object context.');}();function Ra(a,b){this.a=a;this.b=b}t.Object.defineProperties(Ra.prototype,{event:{configurable:!0,enumerable:!0,get:function(){return this.a}},origin:{configurable:!0,enumerable:!0,get:function(){return this.b}}});function J(a){for(var b=[],c=0;c<arguments.length;++c)b[c]=arguments[c];Sa(function(){throw new (Function.prototype.bind.apply(Error,[null,'Could not complete the test successfully - '].concat(q(b))));},function(){return console.error.apply(console,q(b))})}function Ta(a){for(var b=[],c=0;c<arguments.length;++c)b[c]=arguments[c];Sa(function(){},function(){return console.error.apply(console,q(b))})}
function Sa(a,b){'undefined'!==typeof jasmine&&jasmine?a():'undefined'!==typeof console&&console&&console.error&&b()};function Ua(){this.f=[];this.b=[];this.c=[];this.g=[];this.i={};this.a=H()}function Va(a){a.f=[];a.b=[];a.c=[];a.g=[];a.i={};G.adSessionId=null;G.c=Pa();G.o=null;G.G=void 0;G.K=void 0;G.H=null;G.I=null;G.D=null;G.m='foregrounded';G.i='none';G.l='none';G.u=null;G.B=null;G.b=null;G.a=null;G.h=null;G.f=null;G.g=null;G.j=null;G.C=!0;G.v=new Map}
function Wa(a,b){void 0!==a.a&&a.a.adSessionId&&!1!==Xa(a,b)&&a.c.filter(function(c){return c.type===b.event.type}).forEach(function(c){return a.h(c.F,b.event)})}function Ya(a,b){a.f.push(b);Wa(a,b)}function Za(a,b,c){void 0!==a.a&&a.a.adSessionId&&a.f.filter(function(d){return d.event.type===b&&Xa(a,d)}).map(function(d){return d.event}).forEach(c)}
function Xa(a,b){var c=b.event.type,d=-1!==Object.values(A).indexOf(c)&&'volumeChange'!==c;return'impression'===c||'loaded'===c&&a.a.a?b.origin===H().l:d?b.origin===H().i:!0}function $a(a,b,c){'media'===b||'video'===b?ab(a,c):(a.c.push({type:b,F:c}),Za(a,b,c))}function ab(a,b){Object.keys(A).forEach(function(c){c=A[c];a.c.push({type:c,F:b});Za(a,c,b)})}function bb(a,b,c,d){var e={O:c,L:d,F:b};a.g.push(e);a.b.forEach(function(f){var l=cb(f);'sessionStart'===f.event.type&&db(a,l,e);a.h(b,l)})}
function eb(a,b,c){var d=K(a,'sessionError','native',{errorType:b,message:c});a.b.push(d);a.g.forEach(function(e){a.h(e.F,d.event)})}function fb(a,b){a.i=Object.assign(a.i,b);b=a.a.c;if(void 0!==b){b=Object.assign({},gb(a,hb(a,{context:b}),!0),{supportsLoadedEvent:!!a.a.a||'video'==a.a.b});Object.assign(b,{pageUrl:null,contentUrl:a.a.o});var c=K(a,'sessionStart','native',b);a.b.push(c);a.g.forEach(function(d){var e=d.F,f=cb(c);db(a,f,d);a.h(e,f)},a);ib(a)}}
function db(a,b,c){c.O&&(b.data.verificationParameters=a.i[c.O]);c.L&&(c=a.a.v.get(c.L))&&(b.data.verificationParameters=c.verificationParameters,b.data.context.accessMode=c.accessMode,'full'===c.accessMode&&(a.a.g&&(b.data.context.videoElement=a.a.g),a.a.f&&(b.data.context.slotElement=a.a.f)))}function jb(a){var b=a.g,c=K(a,'sessionFinish','native');a.b.push(c);var d=a.a.c;d&&'native'==d.adSessionType||Va(a);b.forEach(function(e){return a.h(e.F,c.event)})}
Ua.prototype.h=function(a,b){for(var c=[],d=1;d<arguments.length;++d)c[d-1]=arguments[d];try{a.apply(null,q(c))}catch(e){Ta(e)}};function kb(a,b){var c=(c=H().D)?Fa(c):void 0;c=gb(a,hb(a,c));Ya(a,K(a,'impression',b,c))}function lb(a,b,c){if(a.a.a||'display'!=a.a.b)b=K(a,'loaded',b,gb(a,hb(a,void 0===c?null:c))),Ya(a,b)}
function mb(a,b,c,d){'start'!==b&&'volumeChange'!==b||null!=(d&&d.deviceVolume)||(d.deviceVolume=a.a.u);if(d&&('start'===b||'volumeChange'===b)){var e=d.videoPlayerVolume,f=d.mediaPlayerVolume;null!=e?(Object.assign(d,{mediaPlayerVolume:e}),a.a.B=e):null!=f&&(Object.assign(d,{videoPlayerVolume:f}),a.a.B=f)}Ya(a,K(a,b,c,d))}
function ib(a){var b=a.f.filter(function(f){return Object.values(A).includes(f.event.type)&&'video'==a.a.b&&f.origin===a.a.i||'loaded'==f.event.type&&'display'==a.a.b&&f.origin===a.a.l?!0:!1}).map(function(f){return f.event}),c=a.a.adSessionId||'',d={};b=p(b);for(var e=b.next();!e.done;d={w:d.w},e=b.next()){d.w=e.value;d.w.adSessionId||(d.w.adSessionId=c);if('loaded'==d.w.type){if(!a.a.a&&'display'==a.a.b)continue;d.w.data=gb(a,hb(a,d.w.data))}a.c.filter(function(f){return function(l){return l.type===
f.w.type}}(d)).forEach(function(f){return function(l){return l.F(f.w)}}(d))}}function nb(a,b,c){a:{c=new Set(c);a=p(a.f.concat(a.b));for(var d=a.next();!d.done;d=a.next())if(d=d.value,c.has(d.event.type)&&d.origin!=b){b=!0;break a}b=!1}return b?(J('Event owner cannot be registered after its events have already been published.'),!1):!0}function ob(a,b){nb(a,b,Object.values(A))&&L(a,b)&&(a.a.i=b)}function pb(a,b){nb(a,b,['impression'])&&qb(a,b)&&(a.a.l=b)}
function qb(a,b){var c=a.a.l;return'none'!=c&&c!=b?(J('Impression event is owned by '+(a.a.l+', not ')+(b+'.')),!1):!0}function L(a,b){var c=a.a.i;return'none'!=c&&c!=b?(J('Media events are owned by '+(a.a.i+', not '+b+'.')),!1):!0}function gb(a,b,c){c=void 0===c?!1:c;b=Object.assign({},b);a.a.b&&Object.assign(b,{mediaType:a.a.b});a.a.a&&(c||'definedByJavaScript'!==a.a.a)&&Object.assign(b,{creativeType:a.a.a});return b}function hb(a,b){return a.a.h?Object.assign({},b,{impressionType:a.a.h}):b}
function K(a,b,c,d){return new Ra({adSessionId:a.a.adSessionId||'',timestamp:(new Date).getTime(),type:b,data:d},c)}function cb(a){a=a.event;return{adSessionId:a.adSessionId,timestamp:a.timestamp,type:a.type,data:a.data}};function rb(a,b,c){'container'===b&&void 0!==a.a.G&&void 0!==a.a&&null!=a.a.adSessionId&&(a.a.H=Ma(a.c,a.a.G,a.a.m,a.a.adSessionId,!0));b=a.a;var d=b.H,e=b.I;if(d)if(e){b=new Ea;var f=d.j,l=d.a,g=d.b,h=e.a,k=e.b;f&&l&&g&&h&&k&&(Ga(b,f),b.l=new B(l,!1),b.v=new B(g,!1),b.i=Object.assign([],d.i),b.c=Object.assign([],d.c),b.h=Object.assign([],d.h),b.o=Object.assign([],d.o),b.f=Object.assign([],e.f,d.f),d=b.l.x,e=b.l.y,h=new B(h,!1),k=new B(k,!1),xa(h,d,e),xa(k,d,e),b.a=h,b.b=Ia(k,g),Ka(b))}else b=d;else b=
null;g=a.a.D;if(b&&!b.J(g)||c)g=Fa(b),c&&(g.adView.reasons=g.adView.reasons||[c]),c=a.b,'audio'!=c.a.a&&Ya(c,K(c,'geometryChange','native',g)),a.a.D=b};function M(a){return'object'===typeof a}function sb(a){return'number'===typeof a&&!isNaN(a)&&0<=a}function N(a){return'string'===typeof a}function O(a,b){return N(a)&&-1!==Object.values(b).indexOf(a)}function tb(a){return!(!a||!a.tagName||'iframe'!==a.tagName.toLowerCase())};function P(a,b,c){this.f=a;this.K=b;this.G=c;this.c=H();this.b=null;this.a=this.g=this.u=void 0;this.I=!0;this.B=void 0;Q(this)}function Q(a){if(!a.b){var b;a:{if((b=a.f.document)&&b.getElementsByClassName&&(b=b.getElementsByClassName('omid-element'))){if(1==b.length){b=b[0];break a}1<b.length&&a.I&&(eb(a.G,'generic',"More than one element with 'omid-element' class name."),a.I=!1)}b=null}if(b&&b.tagName&&'video'===b.tagName.toLowerCase())a.c.g=b;else if(b&&b.tagName)a.c.f=b;else return;ub(a)}}
function ub(a){a.c.g?(a.b=a.c.g,a.i()):a.c.f&&(a.b=a.c.f,tb(a.b)?a.c.j&&a.i():a.i())}function vb(a){a.a&&(tb(a.b)?a.c.j&&(a.D(),wb(a)):(a.D(),wb(a)))}P.prototype.m=function(){this.B&&(this.f.document.removeEventListener('visibilitychange',this.B),this.B=void 0)};P.prototype.i=function(){};function wb(a){a.u&&(a.c.I=a.u,rb(a.K,'creative'))}function xb(a){if(a.a&&a.c.j){var b=new B(a.c.j,!1);xa(b,a.a.x,a.a.y);b.clipsToBounds=!0;return b}};function yb(a,b,c){return zb(a,'setInterval')(b,c)}function Ab(a,b){zb(a,'clearInterval')(b)}function Bb(a,b){zb(a,'clearTimeout')(b)}function zb(a,b){return a.a&&a.a[b]?a.a[b]:Cb(a,b)}
function Db(a,b,c,d){if(a.a.document&&a.a.document.body){var e=a.a.document.createElement('img');e.width=1;e.height=1;e.style.display='none';e.src=b;c&&e.addEventListener('load',function(){return c()});d&&e.addEventListener('error',function(){return d()});a.a.document.body.appendChild(e)}else Cb(a,'sendUrl')(b,c,d)}function Cb(a,b){if(a.a&&a.a.omidNative&&a.a.omidNative[b])return a.a.omidNative[b].bind(a.a.omidNative);throw Error('Native interface method "'+b+'" not found.');};function R(a,b,c,d,e){P.call(this,a,c,e);this.l=b;this.h=void 0;this.j=d}r(R,P);R.prototype.m=function(){void 0!==this.h&&(Ab(this.j,this.h),this.h=void 0);P.prototype.m.call(this)};R.prototype.i=function(){var a=this;P.prototype.i.call(this);null==this.b?this.h=void 0:void 0===this.h&&(this.h=yb(this.j,function(){return Eb(a)},200),Eb(this))};
R.prototype.D=function(){if(this.g){var a=xb(this);if(a){this.a.isCreative=!1;a.isCreative=!0;for(var b=!1,c=0;c<this.a.childViews.length;c++)if(this.a.childViews[c].isCreative){this.a.childViews[c]=a;b=!0;break}b||this.a.childViews.push(a)}else this.a.isCreative=!0;this.u=Ma(this.l,this.g,this.c.m,this.c.adSessionId,this.C())}};R.prototype.C=function(){return!0};
function Eb(a){if(void 0!==a.h){b:{try{var b=a.f.top;var c=0<=b.innerHeight&&0<=b.innerWidth;break b}catch(d){}c=!1}c?(c=a.f.top,c=new B(new Oa(c.innerWidth,c.innerHeight),!1)):c=new B(new Oa(0,0),!1);b=a.b.getBoundingClientRect();if(null==b.x||isNaN(b.x))b.x=b.left;if(null==b.y||isNaN(b.y))b.y=b.top;b=new B(b,!1);c.J(a.g)&&b.J(a.a)||(a.a=b,a.a.clipsToBounds=!0,a.g=c,a.g.childViews.push(a.a),vb(a))}};function S(a,b,c,d){P.call(this,a,c,d);this.o=this.j=this.l=this.h=void 0;this.H=!1;this.v=void 0}r(S,P);S.prototype.m=function(){this.h&&this.h.disconnect();Fb(this);P.prototype.m.call(this)};S.prototype.i=function(){P.prototype.i.call(this);this.b&&(this.h||(this.h=Gb(this)),Hb(this),Ib(this.b)&&Jb(this))};
S.prototype.D=function(){if(this.a&&this.v){var a=xb(this);if(a){var b=a;var c=this.v;var d=Math.max(a.x,c.x);var e=Math.max(a.y,c.y),f=Math.min(a.endX,c.endX);a=Math.min(a.endY,c.endY);f<=d||a<=e?d=null:(c={},d=new B((c.x=d,c.y=e,c.width=Math.abs(f-d),c.height=Math.abs(a-e),c),!1));d||(d=new B({x:0,y:0,width:0,height:0},!1))}else b=this.a,d=this.v;e=new Ea;this.g&&Ga(e,this.g);e.a=b;e.b=d;Ka(e);this.H?za(e.a)?D(e,'hidden'):100===e.m||D(e,'clipped'):D(e,'viewport');this.u=e}};S.prototype.C=function(){return!0};
function Fb(a){a.l&&(a.l.disconnect(),a.l=void 0);a.j&&(a.j.disconnect(),a.j=void 0);a.o&&((0,a.f.removeEventListener)('resize',a.o),a.o=void 0)}function Hb(a){a.h&&a.b&&(a.h.unobserve(a.b),a.h.observe(a.b))}function Ib(a){a=a.getBoundingClientRect();return 0==a.width||0==a.height}
function Gb(a){return new a.f.IntersectionObserver(function(b){try{if(b.length){for(var c,d=b[0],e=1;e<b.length;e++)b[e].time>d.time&&(d=b[e]);c=d;a.g=Kb(c.rootBounds);a.a=Kb(c.boundingClientRect);a.v=Kb(c.intersectionRect);a.H=!!c.isIntersecting;vb(a)}}catch(f){a.m(),eb(a.G,'generic','Problem handling IntersectionObserver callback: '+f.message)}},{root:null,rootMargin:'0px',threshold:[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1]})}
function Jb(a){a.f.ResizeObserver?a.l||(a.l=Mb(a,function(){return Nb(a)}),a.l.observe(a.b)):(a.o||(a.o=function(){return Nb(a)},(0,a.f.addEventListener)('resize',a.o)),a.j||(a.j=new MutationObserver(function(){return Nb(a)}),a.j.observe(a.b,{childList:!1,attributes:!0,subtree:!1})))}function Nb(a){a.b&&!Ib(a.b)&&(Hb(a),Fb(a))}function Mb(a,b){return new a.f.ResizeObserver(b)}function Kb(a){if(a&&null!==a.x&&null!==a.y&&null!==a.width&&null!==a.height)return new B(a,!1)};function Ob(a){return a&&M(a)?Object.entries(a).reduce(function(b,c){var d=p(c);c=d.next().value;d=d.next().value;return b&&N(c)&&null!=d&&M(d)&&N(d.resourceUrl)},!0):!1};function T(a,b,c,d){this.b=a;this.method=b;this.version=c;this.a=d}function Pb(a){return!!a&&void 0!==a.omid_message_guid&&void 0!==a.omid_message_method&&void 0!==a.omid_message_version&&'string'===typeof a.omid_message_guid&&'string'===typeof a.omid_message_method&&'string'===typeof a.omid_message_version&&(void 0===a.omid_message_args||void 0!==a.omid_message_args)}function Qb(a){return new T(a.omid_message_guid,a.omid_message_method,a.omid_message_version,a.omid_message_args)}
function Rb(a){var b={};b=(b.omid_message_guid=a.b,b.omid_message_method=a.method,b.omid_message_version=a.version,b);void 0!==a.a&&(b.omid_message_args=a.a);return b};function Sb(a){this.c=a};function U(a){this.c=a;this.handleExportedMessage=U.prototype.f.bind(this)}r(U,Sb);U.prototype.b=function(a,b){b=void 0===b?this.c:b;if(!b)throw Error('Message destination must be defined at construction time or when sending the message.');b.handleExportedMessage(Rb(a),this)};U.prototype.f=function(a,b){Pb(a)&&this.a&&this.a(Qb(a),b)};function Tb(a,b){this.c=b=void 0===b?I:b;var c=this;a.addEventListener('message',function(d){if('object'===typeof d.data){var e=d.data;Pb(e)&&d.source&&c.a&&c.a(Qb(e),d.source)}})}r(Tb,Sb);Tb.prototype.b=function(a,b){b=void 0===b?this.c:b;if(!b)throw Error('Message destination must be defined at construction time or when sending the message.');b.postMessage(Rb(a),'*')};function Ub(){return'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g,function(a){var b=16*Math.random()|0;return'y'===a?(b&3|8).toString(16):b.toString(16)})};function Vb(a){if(!a.a||!a.a.document)throw Error('OMID Service Script is not running within a window.');var b=a.b;a.b=[];b.forEach(function(c){try{var d=a.c.C?'limited':'full',e=O(c.accessMode,sa)?c.accessMode:null;var f=e?'full'==e&&'limited'==d?d:'domain'==e?'limited':e:d;c.accessMode=f;a:{var l=c.resourceUrl,g=a.a.location.origin;try{var h=new URL(l,g);break a}catch(V){}try{h=new URL(l);break a}catch(V){}h=null}if(d=h){var k=Ub(),m=a.a.document,u=m.createElement('iframe');u.id='omid-verification-script-frame-'+
k;u.style.display='none';['full','limited'].includes(f)?u.srcdoc="<html><head>\n<script type=\"text/javascript\">window['omidVerificationProperties'] = {\n'serviceWindow': window.parent,\n'injectionSource': 'app',\n'injectionId': '"+(k+'\',\n};\x3c/script>\n<script type="text/javascript" src="')+d.href+'">\x3c/script>\n</head><body></body></html>':'domain'==f&&(u.src=Wb(a,k,d).href);['domain','limited'].includes(f)&&(u.sandbox='allow-scripts');m.body.appendChild(u);var w=c.vendorKey,E=c.verificationParameters;
w=void 0===w?'':w;E=void 0===E?'':E;w&&'string'===typeof w&&''!==w&&E&&'string'===typeof E&&''!==E&&(a.f.i[w]=E);a.c.v.set(k,c)}}catch(V){Ta('OMID verification script '+c.resourceUrl+' failed to load: '+V)}})}
function Wb(a,b,c){var d='/.well-known/omid/omloader-v1.html#';(new Map([['verificationScriptUrl',c.href],['injectionId',b]])).forEach(function(e,f){d+=encodeURIComponent(f)+'='+encodeURIComponent(e)+'&'});b=null;try{b=new URL(d,a.a.parent.location.origin)}catch(e){throw Error('OMID Service Script cannot access the parent window.');}return b};function Xb(){var a=Yb,b=Zb,c=this;this.c=X;this.b=a;this.a=H();this.g=b;this.f=!1;this.registerSessionObserver(function(d){return $b(c,d)})}n=Xb.prototype;n.registerSessionObserver=function(a){bb(this.c,a)};n.setSlotElement=function(a){a&&a.tagName?(this.a.f=a,this.b&&ub(this.b)):J('setSlotElement called with a non-HTMLElement.  It will be ignored.')};n.setElementBounds=function(a){this.a.j=a;this.b&&ub(this.b);this.b&&vb(this.b)};n.error=function(a,b){eb(this.c,a,b)};
n.registerAdEvents=function(){pb(this.c,'javascript')};n.registerMediaEvents=function(){ob(this.c,'javascript')};function Y(a,b,c){'impression'==b?qb(a.c,'javascript')&&(kb(a.c,'javascript'),a.b&&Q(a.b)):('loaded'==b?(c=void 0===c?null:c,L(a.c,'javascript')&&lb(a.c,'javascript',c)):L(a.c,'javascript')&&mb(a.c,b,'javascript',c),['loaded','start'].includes(b)&&a.b&&Q(a.b))}n.injectVerificationScriptResources=function(a){var b=this.g;b.b.push.apply(b.b,q(a));if(this.f)try{Vb(this.g)}catch(c){J(c.message)}};
n.setCreativeType=function(a,b){b=void 0===b?null:b;if(!this.a.b||this.a.a)this.a.a=a,'video'==a||'audio'==a?this.a.b='video':'htmlDisplay'==a||'nativeDisplay'==a?this.a.b='display':'definedByJavaScript'==a&&b&&(this.a.b='none'==b?'display':'video')};n.setImpressionType=function(a){if(!this.a.b||this.a.a)this.a.h=a};
function $b(a,b){if('sessionStart'===b.type){a.f=!0;try{Vb(a.g)}catch(c){J(c.message)}}'sessionFinish'===b.type&&(a.f=!1,(b=H().c)&&'native'==b.adSessionType||a.registerSessionObserver(function(c){return $b(a,c)}))}n.setClientInfo=function(a,b,c){var d=this.a.c||{};d.omidJsInfo=Object.assign({},d.omidJsInfo,{sessionClientVersion:a,partnerName:b,partnerVersion:c});this.a.c=d;return this.a.c.omidJsInfo.serviceVersion};function ac(a){return/\d+\.\d+\.\d+(-.*)?/.test(a)}function bc(a){a=a.split('-')[0].split('.');for(var b=['1','0','3'],c=0;3>c;c++){var d=parseInt(a[c],10),e=parseInt(b[c],10);if(d>e)break;else if(d<e)return!1}return!0};function cc(a,b){return ac(a)&&bc(a)?b?b:[]:b&&'string'===typeof b?JSON.parse(b):[]};function dc(){var a=ec;var b=void 0===b?omidGlobal:b;this.a=a;this.f=b;this.b=new U;this.f.omid=this.f.omid||{};this.f.omid.v1_SessionServiceCommunication=this.b;this.c=b&&b.addEventListener&&b.postMessage?new Tb(b):null;this.g=null;this.b.a=this.h.bind(this);this.c&&(this.c.a=this.i.bind(this))}dc.prototype.h=function(a,b){fc(this,a,b,this.b)};
dc.prototype.i=function(a,b){this.g||(this.g=b);this.g!=b?J('The source window of session client post messages cannot be changed from the source of the first message.'):fc(this,a,b,this.c)};
function fc(a,b,c,d){function e(h){for(var k=[],m=0;m<arguments.length;++m)k[m]=arguments[m];k=new T(f,'response',g,ac(g)&&bc(g)?k:JSON.stringify(k));d.b(k,c)}var f=b.b,l=b.method,g=b.version;b=cc(g,b.a);try{gc(a,l,e,b)}catch(h){d.b(new T(f,'error',g,'\n        name: '+h.name+'\n        message: '+h.message+'\n        filename: '+h.filename+'\n        lineNumber: '+h.lineNumber+'\n        columnNumber: '+h.columnNumber+'\n        stack: '+h.stack+'\n        toString(): '+h.toString()),c)}}
function gc(a,b,c,d){switch(b){case 'SessionService.registerAdEvents':a.a.registerAdEvents();break;case 'SessionService.registerMediaEvents':a.a.registerMediaEvents();break;case 'SessionService.registerSessionObserver':a.a.registerSessionObserver(c);break;case 'SessionService.setSlotElement':c=p(d).next().value;a.a.setSlotElement(c);break;case 'SessionService.setVideoElement':c=p(d).next().value;a=a.a;c&&c.tagName&&'video'===c.tagName.toLowerCase()?(a.a.g=c,a.b&&ub(a.b)):J('setVideoElement called with a non-HTMLVideoElement. It will be ignored.');
break;case 'SessionService.setElementBounds':c=p(d).next().value;a.a.setElementBounds(c);break;case 'SessionService.startSession':J('Session start from JS is not supported in mobile app.');break;case 'SessionService.finishSession':J('Session finish from JS is not supported in mobile app.');break;case 'SessionService.impressionOccurred':Y(a.a,'impression');break;case 'SessionService.loaded':(c=p(d).next().value)?(b={skippable:c.isSkippable,autoPlay:c.isAutoPlay,position:c.position},c.isSkippable&&
(b.skipOffset=c.skipOffset),Y(a.a,'loaded',b)):Y(a.a,'loaded');break;case 'SessionService.start':b=p(d);c=b.next().value;b=b.next().value;Y(a.a,'start',{duration:c,mediaPlayerVolume:b});break;case 'SessionService.firstQuartile':Y(a.a,'firstQuartile');break;case 'SessionService.midpoint':Y(a.a,'midpoint');break;case 'SessionService.thirdQuartile':Y(a.a,'thirdQuartile');break;case 'SessionService.complete':Y(a.a,'complete');break;case 'SessionService.pause':Y(a.a,'pause');break;case 'SessionService.resume':Y(a.a,
'resume');break;case 'SessionService.bufferStart':Y(a.a,'bufferStart');break;case 'SessionService.bufferFinish':Y(a.a,'bufferFinish');break;case 'SessionService.skipped':Y(a.a,'skipped');break;case 'SessionService.volumeChange':c={mediaPlayerVolume:p(d).next().value};Y(a.a,'volumeChange',c);break;case 'SessionService.playerStateChange':c={state:p(d).next().value};Y(a.a,'playerStateChange',c);break;case 'SessionService.adUserInteraction':c={interactionType:p(d).next().value};Y(a.a,'adUserInteraction',
c);break;case 'SessionService.setClientInfo':var e=p(d);b=e.next().value;d=e.next().value;e=e.next().value;a=a.a.setClientInfo(b,d,e);c(a);break;case 'SessionService.injectVerificationScriptResources':c=p(d).next().value;a.a.injectVerificationScriptResources(c);break;case 'SessionService.setCreativeType':c=p(d).next().value;a.a.setCreativeType(c);break;case 'SessionService.setImpressionType':c=p(d).next().value;a.a.setImpressionType(c);break;case 'SessionService.setContentUrl':c=p(d).next().value;
a.a.a.o=c;break;case 'SessionService.sessionError':b=p(d),c=b.next().value,b=b.next().value,a.a.error(c,b)}};function Z(){var a=X,b=hc,c=ic,d=Yb;this.f=ec;this.a=a;this.c=b;this.h=c;this.g=d;this.b=H()}n=Z.prototype;
n.T=function(a){if(!(!(a&&M(a)&&O(a.impressionOwner,ra))||'videoEventsOwner'in a&&null!=a.videoEventsOwner&&!O(a.videoEventsOwner,ra)||'mediaEventsOwner'in a&&null!=a.mediaEventsOwner&&!O(a.mediaEventsOwner,ra))){if(a.creativeType&&a.impressionType){var b=a.mediaEventsOwner;null==this.b.a&&this.f.setCreativeType(a.creativeType,b);null==this.b.h&&(this.b.h=a.impressionType);ob(this.a,b)}else b=a.videoEventsOwner,this.b.b=null==b||'none'===b?'display':'video',this.b.a=null,this.b.h=null,ob(this.a,b);
pb(this.a,a.impressionOwner);a&&null!=a.isolateVerificationScripts&&'boolean'===typeof a.isolateVerificationScripts&&(this.b.C=a.isolateVerificationScripts)}};
n.W=function(a,b,c,d){var e;if(M(b)){if(e=O(b.environment,ua)&&O(b.adSessionType,pa))e=b.omidNativeInfo,e=M(e)?N(e.partnerName)&&N(e.partnerVersion):!1;e&&(e=b.app,e=M(e)?N(e.libraryVersion)&&N(e.appId):!1)}else e=!1;e&&(Ob(d)&&(this.b.v=new Map(Object.entries(d))),d=this.f,c=void 0===c?null:c,null==a&&(a=Ub()),b.canMeasureVisibility=d.b.C(),d.a.adSessionId=a,a=d.a,e=b,void 0!==e.contentUrl&&(a.o=e.contentUrl,e.contentUrl=void 0),e=a.c||{},b.omidJsInfo=Object.assign({},e.omidJsInfo||{},b.omidJsInfo||
{}),e=b=Object.assign({},e,b),a.C||(null!=a.g?(e.videoElement=a.g,e.accessMode='full'):null!=a.f&&(e.slotElement=a.f,e.accessMode='full')),a.c=b,fb(d.c,c),d.b&&Q(d.b))};n.U=function(){var a=this.f;jb(a.c);a.b.m()};n.$=function(a){M(a)&&sb(a.x)&&sb(a.y)&&sb(a.width)&&sb(a.height)&&(this.b.G=a,rb(this.c,'container'))};n.aa=function(a){O(a,ta)&&(this.b.m=a,'backgrounded'===a?rb(this.c,'container','backgrounded'):rb(this.c,'container'))};n.X=function(a){'impression'===a&&(this.M(),this.g&&Q(this.g))};
n.M=function(){qb(this.a,'native')&&kb(this.a,'native')};n.V=function(a){a=void 0===a?null:a;L(this.a,'native')&&lb(this.a,'native',a)};n.error=function(a,b){O(a,oa)&&eb(this.a,a,b)};n.Y=function(a,b){this.N(a,b)};n.N=function(a,b){L(this.a,'native')&&O(a,A)&&(void 0===b||M(b))&&('loaded'==a?lb(this.a,'native',b):mb(this.a,a,'native',b))};
n.Z=function(a){if('none'!==this.a.a.i&&'number'===typeof a&&!isNaN(a)){this.b.u=a;a=this.h;var b=a.a.B;null!=b&&mb(a.b,'volumeChange','native',{mediaPlayerVolume:b,deviceVolume:a.a.u})}};Z.prototype.startSession=Z.prototype.W;Z.prototype.error=Z.prototype.error;Z.prototype.finishSession=Z.prototype.U;Z.prototype.publishAdEvent=Z.prototype.X;Z.prototype.publishImpressionEvent=Z.prototype.M;Z.prototype.publishVideoEvent=Z.prototype.Y;Z.prototype.publishMediaEvent=Z.prototype.N;
Z.prototype.publishLoadedEvent=Z.prototype.V;Z.prototype.setNativeViewHierarchy=Z.prototype.$;Z.prototype.setState=Z.prototype.aa;Z.prototype.setDeviceVolume=Z.prototype.Z;Z.prototype.init=Z.prototype.T;function jc(){var a=X,b=kc;var c=void 0===c?I:c;this.g=a;this.a=b;this.h={};this.f={};this.c=new U;c.omid=c.omid||{};c.omid.v1_VerificationServiceCommunication=this.c;this.b=null;c&&c.addEventListener&&c.postMessage&&(this.b=new Tb(c));this.c.a=this.i.bind(this);this.b&&(this.b.a=this.j.bind(this))}function lc(a,b,c,d){Db(a.a,b,c,d)}function mc(a,b,c,d){Cb(a.a,'downloadJavaScriptResource')(b,c,d)}jc.prototype.j=function(a,b){this.b&&nc(this,a,b,this.b)};jc.prototype.i=function(a,b){nc(this,a,b,this.c)};
function nc(a,b,c,d){function e(F){for(var W=[],qa=0;qa<arguments.length;++qa)W[qa]=arguments[qa];W=new T(f,'response',g,ac(g)&&bc(g)?W:JSON.stringify(W));d.b(W,c)}var f=b.b,l=b.method,g=b.version;b=cc(g,b.a);try{switch(l){case 'VerificationService.addEventListener':var h=p(b).next().value;$a(a.g,h,e);break;case 'VerificationService.addSessionListener':var k=p(b),m=k.next().value,u=k.next().value;bb(a.g,e,m,u);break;case 'VerificationService.sendUrl':var w=p(b).next().value;lc(a,w,function(){return e(!0)},
function(){return e(!1)});break;case 'VerificationService.setTimeout':var E=p(b),V=E.next().value,sc=E.next().value;a.h[V]=zb(a.a,'setTimeout')(e,sc);break;case 'VerificationService.clearTimeout':var tc=p(b).next().value;Bb(a.a,a.h[tc]);break;case 'VerificationService.setInterval':var Lb=p(b),uc=Lb.next().value,vc=Lb.next().value;a.f[uc]=yb(a.a,e,vc);break;case 'VerificationService.clearInterval':var wc=p(b).next().value;Ab(a.a,a.f[wc]);break;case 'VerificationService.injectJavaScriptResource':var xc=
p(b).next().value;mc(a,xc,function(F){return e(!0,F)},function(){return e(!1)});break;case 'VerificationService.getVersion':p(b).next();var yc=H().c.omidJsInfo;e(yc.serviceVersion)}}catch(F){d.b(new T(f,'error',g,'\n              name: '+F.name+'\n              message: '+F.message+'\n              filename: '+F.filename+'\n              lineNumber: '+F.lineNumber+'\n              columnNumber: '+F.columnNumber+'\n              stack: '+F.stack+'\n              toString(): '+F.toString()+'\n          '),
c)}};function oc(){var a=I.document.createElement('iframe');a.id='omid_v1_present';a.name='omid_v1_present';a.style.display='none';I.document.body.appendChild(a)}function pc(){var a=new MutationObserver(function(b){b.forEach(function(c){'BODY'===c.addedNodes[0].nodeName&&(oc(),a.disconnect())})});a.observe(I.document.documentElement,{childList:!0})};var X=new Ua,kc=new function(){var a;this.a=a=void 0===a?omidGlobal:a};new jc;var qc=new function(){},rc=new function(){},hc=new function(){this.b=X;this.c=rc;this.a=H()},zc;I?zc=I.IntersectionObserver&&(I.MutationObserver||I.ResizeObserver)?new S(I,qc,hc,X):new R(I,rc,hc,kc,X):zc=null;var Yb=zc,Zb=new function(){var a=X;var b=void 0===b?I:b;this.f=a;this.a=b;this.c=H();this.b=[]},ec=new Xb,ic=new function(){var a=X,b=H();this.b=a;this.a=b};I.omidBridge=new Z;new dc;
if(I.frames&&I.document&&!('omid_v1_present'in I.frames)){var Ac;if(Ac=null==I.document.body)Ac='MutationObserver'in I;Ac?pc():I.document.body?oc():I.document.write('<iframe style="display:none" id="omid_v1_present" name="omid_v1_present"></iframe>')};
}).call(this, this);

