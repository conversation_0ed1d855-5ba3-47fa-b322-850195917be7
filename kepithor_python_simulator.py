#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kepithor Python Simulator - Multi-threaded Implementation
شبیه‌ساز کامل Kepithor با 80 thread همزمان
"""

import requests
import json
import time
import random
import uuid
import threading
import queue
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib
import hmac

class DeviceGenerator:
    """تولید اطلاعات دستگاه رندم"""
    
    @staticmethod
    def generate_device_info():
        """تولید اطلاعات دستگاه اندروید رندم"""
        devices = [
            {"model": "SM-G973F", "brand": "Samsung", "device": "beyond1lte"},
            {"model": "Pixel 4", "brand": "Google", "device": "flame"},
            {"model": "OnePlus 8", "brand": "OnePlus", "device": "instantnoodle"},
            {"model": "Mi 10", "brand": "<PERSON>mi", "device": "umi"},
            {"model": "SM-A515F", "brand": "Samsung", "device": "a51"},
            {"model": "Redmi Note 9", "brand": "Xiaomi", "device": "merlin"},
            {"model": "Galaxy A21s", "brand": "Samsung", "device": "a21s"},
            {"model": "Pixel 5", "brand": "Google", "device": "redfin"},
        ]
        
        device = random.choice(devices)
        android_versions = ["10", "11", "12", "13"]
        
        return {
            "device_id": str(uuid.uuid4()),
            "android_id": ''.join(random.choices('**********abcdef', k=16)),
            "model": device["model"],
            "brand": device["brand"],
            "device": device["device"],
            "version": random.choice(android_versions),
            "sdk_int": random.randint(29, 33),
            "screen_density": random.choice([2.0, 2.75, 3.0]),
            "screen_width": random.choice([1080, 1440]),
            "screen_height": random.choice([1920, 2560, 3040]),
            "manufacturer": device["brand"],
            "fingerprint": f"{device['brand']}/{device['device']}/{device['device']}:11/RKQ1.200826.002/**********:user/release-keys",
            "imei": ''.join(random.choices('**********', k=15)),
            "mac_address": ':'.join(['%02x' % random.randint(0, 255) for _ in range(6)])
        }

class KepithorAccount:
    """کلاس برای مدیریت یک اکانت Kepithor"""
    
    def __init__(self, account_id, username, password):
        """راه‌اندازی اکانت"""
        self.account_id = account_id
        self.username = username
        self.password = password
        self.device_info = DeviceGenerator.generate_device_info()
        self.session = requests.Session()
        self.auth_token = None
        self.balance = 0
        self.ads_watched = 0
        self.total_earned = 0
        self.is_logged_in = False
        self.last_activity = None
        
        # تنظیم headers
        self.setup_headers()
    
    def setup_headers(self):
        """تنظیم headers برای درخواست‌ها"""
        self.session.headers.update({
            'User-Agent': f'KepithorApp/1.0 (Android {self.device_info["version"]}; {self.device_info["model"]})',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Device-ID': self.device_info['device_id'],
            'X-Android-ID': self.device_info['android_id'],
            'X-App-Version': '1.59',
            'X-Platform': 'android',
            'X-Device-Model': self.device_info['model'],
            'X-Device-Brand': self.device_info['brand'],
            'X-IMEI': self.device_info['imei'],
            'Connection': 'keep-alive'
        })
    
    def login(self):
        """لاگین به اکانت"""
        try:
            login_data = {
                'username': self.username,
                'password': self.password,
                'device_info': self.device_info,
                'timestamp': int(time.time())
            }
            
            # فرض: API endpoint واقعی
            response = self.session.post(
                'https://api.kepithor.com/auth/login',
                json=login_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                self.auth_token = result.get('token')
                self.balance = result.get('balance', 0)
                
                # اضافه کردن token به headers
                if self.auth_token:
                    self.session.headers['Authorization'] = f'Bearer {self.auth_token}'
                
                self.is_logged_in = True
                self.last_activity = datetime.now()
                
                print(f"✅ [{self.account_id}] Login successful! Balance: {self.balance}")
                return True
            else:
                print(f"❌ [{self.account_id}] Login failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ [{self.account_id}] Login error: {e}")
            return False
    
    def get_balance(self):
        """دریافت موجودی فعلی"""
        try:
            response = self.session.get(
                'https://api.kepithor.com/user/balance',
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                self.balance = result.get('balance', self.balance)
                return self.balance
            else:
                return None
                
        except Exception as e:
            print(f"❌ [{self.account_id}] Balance error: {e}")
            return None
    
    def get_available_ads(self):
        """دریافت تبلیغات موجود"""
        try:
            response = self.session.get(
                'https://api.kepithor.com/ads/available',
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('ads', [])
            else:
                return []
                
        except Exception as e:
            print(f"❌ [{self.account_id}] Ads error: {e}")
            return []
    
    def watch_ad(self, ad_id):
        """تماشای تبلیغ"""
        try:
            watch_data = {
                'ad_id': ad_id,
                'duration': random.randint(25, 35),
                'completion_rate': 100,
                'device_info': self.device_info,
                'timestamp': int(time.time())
            }
            
            response = self.session.post(
                'https://api.kepithor.com/ads/watch',
                json=watch_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                reward = result.get('reward', 0)
                self.balance += reward
                self.ads_watched += 1
                self.total_earned += reward
                self.last_activity = datetime.now()
                
                return {
                    'success': True,
                    'reward': reward,
                    'new_balance': self.balance
                }
            else:
                return {'success': False, 'reward': 0}
                
        except Exception as e:
            print(f"❌ [{self.account_id}] Watch ad error: {e}")
            return {'success': False, 'reward': 0}
    
    def claim_rewards(self):
        """دریافت جوایز"""
        try:
            response = self.session.post(
                'https://api.kepithor.com/rewards/claim',
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                bonus = result.get('bonus', 0)
                self.balance += bonus
                self.total_earned += bonus
                return bonus
            else:
                return 0
                
        except Exception as e:
            print(f"❌ [{self.account_id}] Claim error: {e}")
            return 0

class KepithorSimulatorManager:
    """مدیریت 80 اکانت همزمان"""
    
    def __init__(self, max_workers=80):
        """راه‌اندازی مدیر"""
        self.max_workers = max_workers
        self.accounts = []
        self.results = {}
        self.stats = {
            'total_accounts': 0,
            'successful_logins': 0,
            'total_ads_watched': 0,
            'total_earned': 0,
            'active_accounts': 0
        }
        self.running = False
        
    def add_account(self, account_id, username, password):
        """اضافه کردن اکانت"""
        account = KepithorAccount(account_id, username, password)
        self.accounts.append(account)
        self.stats['total_accounts'] += 1
        print(f"➕ Account {account_id} added")
    
    def add_bulk_accounts(self, accounts_list):
        """اضافه کردن چندین اکانت"""
        for account_data in accounts_list:
            self.add_account(
                account_data['id'],
                account_data['username'], 
                account_data['password']
            )
    
    def worker_function(self, account, duration_minutes=60):
        """تابع worker برای هر thread"""
        try:
            # لاگین
            if not account.login():
                return {
                    'account_id': account.account_id,
                    'success': False,
                    'error': 'Login failed'
                }
            
            self.stats['successful_logins'] += 1
            
            # شروع farming
            start_time = time.time()
            end_time = start_time + (duration_minutes * 60)
            
            ads_watched = 0
            total_earned = 0
            
            while time.time() < end_time and self.running:
                # دریافت تبلیغات موجود
                ads = account.get_available_ads()
                
                if ads:
                    # انتخاب تبلیغ رندم
                    ad = random.choice(ads)
                    result = account.watch_ad(ad.get('id', f'ad_{random.randint(1000, 9999)}'))
                    
                    if result['success']:
                        ads_watched += 1
                        total_earned += result['reward']
                        self.stats['total_ads_watched'] += 1
                        self.stats['total_earned'] += result['reward']
                        
                        print(f"🎯 [{account.account_id}] Ad watched! Reward: {result['reward']}, Total: {account.balance}")
                    
                    # تأخیر بین تبلیغات
                    delay = random.randint(30, 120)
                    time.sleep(delay)
                else:
                    # اگر تبلیغی نیست، صبر کن
                    time.sleep(300)  # 5 دقیقه
                
                # بررسی جوایز اضافی
                if random.random() < 0.1:  # 10% احتمال
                    bonus = account.claim_rewards()
                    if bonus > 0:
                        total_earned += bonus
                        print(f"🎁 [{account.account_id}] Bonus claimed: {bonus}")
            
            return {
                'account_id': account.account_id,
                'success': True,
                'ads_watched': ads_watched,
                'total_earned': total_earned,
                'final_balance': account.balance,
                'duration': duration_minutes
            }
            
        except Exception as e:
            return {
                'account_id': account.account_id,
                'success': False,
                'error': str(e)
            }
    
    def run_all_accounts(self, duration_minutes=60):
        """اجرای همه اکانت‌ها همزمان"""
        print(f"🚀 Starting {len(self.accounts)} accounts for {duration_minutes} minutes...")
        
        self.running = True
        self.stats['active_accounts'] = len(self.accounts)
        
        # اجرای همزمان با ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # ارسال تمام کارها
            future_to_account = {
                executor.submit(self.worker_function, account, duration_minutes): account 
                for account in self.accounts
            }
            
            # جمع‌آوری نتایج
            for future in as_completed(future_to_account):
                account = future_to_account[future]
                try:
                    result = future.result()
                    self.results[result['account_id']] = result
                    
                    if result['success']:
                        print(f"✅ [{result['account_id']}] Completed: {result['ads_watched']} ads, ${result['total_earned']/10000:.2f}")
                    else:
                        print(f"❌ [{result['account_id']}] Failed: {result.get('error', 'Unknown error')}")
                        
                except Exception as e:
                    print(f"❌ [{account.account_id}] Exception: {e}")
        
        self.running = False
        self.print_final_report()
    
    def print_final_report(self):
        """چاپ گزارش نهایی"""
        print("\n" + "="*80)
        print("📊 FINAL REPORT - KEPITHOR PYTHON SIMULATOR")
        print("="*80)
        
        successful_accounts = [r for r in self.results.values() if r['success']]
        
        print(f"📱 Total Accounts: {self.stats['total_accounts']}")
        print(f"✅ Successful Logins: {self.stats['successful_logins']}")
        print(f"🎯 Successful Runs: {len(successful_accounts)}")
        print(f"📺 Total Ads Watched: {sum(r['ads_watched'] for r in successful_accounts)}")
        print(f"💰 Total Points Earned: {sum(r['total_earned'] for r in successful_accounts):,}")
        print(f"💵 Total Dollar Value: ${sum(r['total_earned'] for r in successful_accounts)/10000:.2f}")
        
        if successful_accounts:
            avg_ads = sum(r['ads_watched'] for r in successful_accounts) / len(successful_accounts)
            avg_earned = sum(r['total_earned'] for r in successful_accounts) / len(successful_accounts)
            
            print(f"\n📈 AVERAGES:")
            print(f"📺 Avg Ads per Account: {avg_ads:.1f}")
            print(f"💰 Avg Points per Account: {avg_earned:.0f}")
            print(f"💵 Avg Dollars per Account: ${avg_earned/10000:.2f}")
        
        print("\n🎉 Simulation completed successfully!")

def main():
    """تابع اصلی برای تست"""
    print("🎮 Kepithor Python Simulator - 80 Accounts")
    print("=" * 60)
    
    # ساخت مدیر
    manager = KepithorSimulatorManager(max_workers=80)
    
    # اضافه کردن اکانت‌های تست (فرضی)
    test_accounts = []
    for i in range(80):
        test_accounts.append({
            'id': f'account_{i+1:03d}',
            'username': f'user{i+1:03d}@example.com',
            'password': f'password{i+1:03d}'
        })
    
    manager.add_bulk_accounts(test_accounts)
    
    # اجرای شبیه‌سازی
    try:
        manager.run_all_accounts(duration_minutes=60)  # 1 ساعت
    except KeyboardInterrupt:
        print("\n🛑 Simulation stopped by user")
        manager.running = False

if __name__ == "__main__":
    main()
