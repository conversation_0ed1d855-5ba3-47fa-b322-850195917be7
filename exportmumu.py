import pygetwindow as gw
import win32gui, win32con, win32process, win32api
import ctypes, time, os, threading, re, sqlite3, sys
from PIL import ImageGrab
import pytesseract
import tkinter as tk
import threading
import time
import socket
import json
import math


from tkinter import ttk, messagebox, simpledialog
from datetime import datetime,timedelta

pytesseract.pytesseract.tesseract_cmd = r"C:\\Program Files\\Tesseract-OCR\\tesseract.exe"
os.environ['TESSDATA_PREFIX'] = r"C:\\Program Files\\Tesseract-OCR\\tessdata"
user32 = ctypes.windll.user32

results = []
running = False
auto_scan_running = False
auto_scan_thread = None
auto_scan_interval = 5  # دقیقه پیش‌فرض
clients_info = {}  # key: ip, value: {connected_time, accounts_running, db_data, last_seen}

SERVER_PORT = 5000
server_socket = None
server_thread = None
def get_app_dir():
    return os.path.dirname(sys.executable) if getattr(sys, 'frozen', False) else os.path.dirname(os.path.abspath(__file__))

DB_PATH = os.path.join(get_app_dir(), "crowns.db")

def show_messagebox(self, title, message):
    messagebox.showinfo(title, message)



# ---- تابع چک و ایجاد ستون‌های ضروری ----
def ensure_database_columns():


    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # ساخت جدول پایه اگر وجود نداشت
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS crowns (
            title TEXT PRIMARY KEY
        )
    ''')
    conn.commit()

    # گرفتن لیست ستون‌های فعلی
    cursor.execute("PRAGMA table_info(crowns)")
    columns = [info[1] for info in cursor.fetchall()]

    # ستون‌هایی که باید وجود داشته باشن
    required_columns = {
        'value': 'REAL DEFAULT 0',
        'dollars': 'REAL DEFAULT 0',
        'adverts': 'INTEGER DEFAULT 0',
        'last_updated': 'TEXT'
    }

    for column, coltype in required_columns.items():
        if column not in columns:
            try:
                cursor.execute(f"ALTER TABLE crowns ADD COLUMN {column} {coltype}")
                print(f"✅ ستون {column} اضافه شد.")
            except Exception as e:
                print(f"⚠️ خطا در اضافه کردن ستون {column}: {e}")

    conn.commit()
    conn.close()

def auto_scan_loop():
    global auto_scan_running
    while auto_scan_running:
        print("⏱️ شروع اسکن خودکار...")
        start_processing()
        time.sleep(auto_scan_interval * 60)
def toggle_auto_scan():
    global auto_scan_running, auto_scan_thread
    if auto_scan_running:
        auto_scan_running = False
        btn_auto_scan.config(text="🕒 شروع اسکن خودکار", bg="#0097a7")
        messagebox.showinfo("متوقف شد", "اسکن خودکار متوقف شد.")
    else:
        try:
            interval = int(entry_interval.get())
            if interval < 1:
                raise ValueError
        except ValueError:
            messagebox.showerror("خطا", "بازه زمانی باید عدد صحیح مثبت باشد.")
            return
        auto_scan_interval = interval
        auto_scan_running = True
        auto_scan_thread = threading.Thread(target=auto_scan_loop, daemon=True)
        auto_scan_thread.start()
        btn_auto_scan.config(text="⛔ توقف اسکن خودکار", bg="#d32f2f")

# ---- توابع اصلی برنامه ----
def force_foreground_and_maximize(hwnd):
    try:
        fg_window = win32gui.GetForegroundWindow()
        fg_thread_id, _ = win32process.GetWindowThreadProcessId(fg_window)
        target_thread_id, _ = win32process.GetWindowThreadProcessId(hwnd)
        current_thread_id = win32api.GetCurrentThreadId()

        user32.AttachThreadInput(current_thread_id, target_thread_id, True)
        user32.AttachThreadInput(fg_thread_id, target_thread_id, True)
        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
        win32gui.SetForegroundWindow(hwnd)
        time.sleep(0.5)
        win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)
        user32.AttachThreadInput(current_thread_id, target_thread_id, False)
        user32.AttachThreadInput(fg_thread_id, target_thread_id, False)
    except Exception as e:
        print(f"❌ خطا در فوکوس/ماکزیمایز: {e}")

def extract_crowns_from_window(hwnd):
    try:
        left, top, right, bottom = win32gui.GetWindowRect(hwnd)
        screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
        width, height = screenshot.size
        crowns_crop = screenshot.crop((int(width * 0.55), int(height * 0.08), int(width * 0.75), int(height * 0.18)))
        crowns_gray = crowns_crop.convert("L")
        crowns_binary = crowns_gray.point(lambda p: 255 if p > 150 else 0)
        text = pytesseract.image_to_string(crowns_binary, lang='eng')
        numbers = re.findall(r"\d+\.\d+", text)
        return numbers[0] if numbers else "0"
    except Exception:
        return "0"

def extract_adverts_from_window(hwnd):
    try:
        left, top, right, bottom = win32gui.GetWindowRect(hwnd)
        screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
        width, height = screenshot.size

        # ناحیه احتمالی عدد تبلیغ

        adverts_crop = screenshot.crop(( int(width * 0.58),   int(height * 0.405),   int(width * 0.735), int(height * 0.445) ))


        adverts_gray = adverts_crop.convert("L")
        adverts_binary = adverts_gray.point(lambda p: 255 if p > 150 else 0)

        text = pytesseract.image_to_string(adverts_binary, config="--psm 7 digits", lang='eng')
        numbers = re.findall(r"\d+", text)
        return int(numbers[0]) if numbers else 0

    except Exception as e:
        print(f"❌ خطا در OCR تبلیغات: {e}")
        return 0


def extract_number_from_title(title):
    match = re.search(r"(\d+)-(\d+)", title)
    return int(match.group(1)) * 100 + int(match.group(2)) if match else 999999

def filter_windows_by_range(windows, start, end):
    return [w for w in windows if (m := re.search(r"(\d+)-(\d+)", w.title)) and start <= int(m.group(2)) <= end]

def save_to_database(title, crowns, dollars, adverts):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    cursor.execute('''
        INSERT INTO crowns (title, value, dollars, adverts, last_updated)
        VALUES (?, ?, ?, ?, ?)
        ON CONFLICT(title) DO UPDATE SET
            value=excluded.value,
            dollars=excluded.dollars,
            adverts=excluded.adverts,
            last_updated=excluded.last_updated
    ''', (title, crowns, dollars, adverts, now))
    conn.commit()
    conn.close()

def edit_record(title):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM crowns WHERE title=?", (title,))
    row = cursor.fetchone()
    conn.close()
    if not row:
        messagebox.showerror("خطا", "رکورد پیدا نشد.")
        return
    new_value = simpledialog.askfloat("ویرایش Crown", f"مقدار جدید برای {title}:", initialvalue=row[1])
    if new_value is not None:
        new_dollars = new_value / 10000
        save_to_database(title, new_value, new_dollars, row[3])
        messagebox.showinfo("موفقیت", "رکورد بروزرسانی شد.")

def get_existing_data(title):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT value, dollars, adverts FROM crowns WHERE title=?", (title,))
    row = cursor.fetchone()
    conn.close()
    return row if row else (0, 0, 0)

def process_windows(limit, range_start, range_end):
    global results, running
    results.clear()
    mumu_windows = [w for w in gw.getAllWindows() if w.title.startswith("MuMu Player")]
    total_label.config(text=f"🔍 تعداد کل شبیه‌سازها: {len(mumu_windows)}")
    if range_filter_var.get() and (range_start is not None and range_end is not None):
        mumu_windows = filter_windows_by_range(mumu_windows, range_start, range_end)
    mumu_windows = sorted(mumu_windows, key=lambda x: extract_number_from_title(x.title))
    if limit_filter_var.get() and limit is not None:
        mumu_windows = mumu_windows[:limit]
    total, total_dollars = 0, 0
    for win in mumu_windows:
        if not running:
            break
        force_foreground_and_maximize(win._hWnd)
        time.sleep(1)

        crowns_raw = extract_crowns_from_window(win._hWnd)
        adverts_raw = extract_adverts_from_window(win._hWnd)

        try:
            value = float(crowns_raw)
            if value == 0:
                raise ValueError("امتیاز صفر، استفاده از مقدار قبلی")
            dollars = value / 10000
        except:
            # استفاده از مقدار قبلی در صورت OCR ناموفق
            value, dollars, _ = get_existing_data(win.title)

        try:
            adverts = int(adverts_raw)
        except:
            _, _, adverts = get_existing_data(win.title)

        save_to_database(win.title, value, dollars, adverts)
        save_daily_stat(win.title, value, dollars, adverts)

        results.append((win.title, value, dollars, adverts))

        total += value
        total_dollars += dollars

    update_treeview()
    total_crowns_label.config(text=f"👑 مجموع Crownها: {total:.1f}")
    total_dollar_label.config(text=f"💰 مجموع دلاری: {total_dollars:.2f}$")
def get_daily_date():
    now = datetime.now()
    if now.hour < 12:
        target_date = now.date() - timedelta(days=1)
    else:
        target_date = now.date()
    return str(target_date)

def ensure_daily_table():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS daily_stats (
            title TEXT,
            date TEXT,
            value REAL DEFAULT 0,
            dollars REAL DEFAULT 0,
            adverts INTEGER DEFAULT 0,
            PRIMARY KEY (title, date)
        )
    ''')
    conn.commit()
    conn.close()
# ---- اجرای چک ستون‌ها ----
ensure_database_columns()
ensure_daily_table()

def create_treeview():
    # در اینجا ویجت tree را تعریف می‌کنید
    columns = ("پنجره", "Crowns", "دلار", "تعداد تبلیغات")
    tree = ttk.Treeview(frame_tree, columns=columns, show="headings", height=10)

    # افزودن مرتب‌سازی به ستون‌ها
    tree.heading("پنجره", text="نام شبیه‌ساز", command=lambda: sort_column(tree, "پنجره", False))
    tree.heading("Crowns", text="امتیاز", command=lambda: sort_column(tree, "Crowns", False))
    tree.heading("دلار", text="دلار", command=lambda: sort_column(tree, "دلار", False))
    tree.heading("تعداد تبلیغات", text="تعداد تبلیغات", command=lambda: sort_column(tree, "تعداد تبلیغات", False))

    # تنظیمات style برای Treeview
    style = ttk.Style()
    style.theme_use("clam")
    style.configure("Treeview", background="#ffffff", foreground="black", rowheight=25, fieldbackground="#fbe9e7")
    style.map('Treeview', background=[('selected', '#ffab91')])

    # ایجاد اسکرول بار
    scrollbar = ttk.Scrollbar(frame_tree, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)
    scrollbar.pack(side="right", fill="y")
    tree.pack(fill="both", expand=True)

    return tree

def delete_db_rows_after_80(self):
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # حذف تمام ردیف‌هایی که id > 80 یا rowid > 80
        cursor.execute("DELETE FROM crowns WHERE rowid > 80")
        conn.commit()
        conn.close()

        messagebox.showinfo("حذف موفق", "تمام ردیف‌های ۸۱ به بعد از دیتابیس حذف شدند.")
        self.update_status("حذف ردیف‌های ۸۱ به بعد از دیتابیس انجام شد")
    except Exception as e:
        messagebox.showerror("خطا در حذف", f"مشکلی در حذف داده‌ها از دیتابیس رخ داد:\n{e}")


def show_forecast_to_2dollars():
    def sort_column(col, reverse):
        data = [(tree.set(k, col), k) for k in tree.get_children("")]
        try:
            data.sort(key=lambda t: float(t[0].replace("$", "")) if t[0].replace("$", "").replace(".", "", 1).isdigit() else t[0], reverse=reverse)
        except:
            data.sort(reverse=reverse)
        for index, (val, k) in enumerate(data):
            tree.move(k, '', index)
        tree.heading(col, command=lambda: sort_column(col, not reverse))

    def apply_filter():
        show_only_pending = var_only_pending.get()
        show_only_reached = var_only_reached.get()

        total_reached_dollars = 0
        total_pending_count = 0

        for item in tree.get_children():
            tree.delete(item)

        for title, value in rows:
            if value >= threshold_value:
                status = "✅ رسیده"
                days_left = 0
                if show_only_pending:
                    continue
                dollars = value / 10000
                total_reached_dollars += dollars
            else:
                if show_only_reached:
                    continue
                remaining = threshold_value - value
                days_left = math.ceil(remaining / daily_growth)
                status = "⏳ در حال رسیدن"
                dollars = value / 10000
                total_pending_count += 1

            item = tree.insert("", tk.END, values=(
                title,
                status,
                days_left,
                round(value),
                f"{dollars:.2f}$"
            ))

            # رنگ‌بندی ردیف‌ها
            if value >= threshold_value:
                tree.item(item, tags=("done",))
            else:
                tree.item(item, tags=("pending",))

        lbl_summary.config(text=f"💰 مجموع دلار اکانت‌های رسیده: {total_reached_dollars:.2f}$ | ⏳ تعداد اکانت‌های در حال رسیدن: {total_pending_count}")

    top = tk.Toplevel(root)
    top.title("پیش‌بینی رسیدن اکانت‌ها به 2 دلار")
    top.geometry("780x550")
    top.configure(bg="#f1f8e9")

    daily_growth = 1000  # رشد روزانه فرضی
    threshold_value = 20000

    var_only_pending = tk.BooleanVar()
    var_only_reached = tk.BooleanVar()

    filter_frame = tk.Frame(top, bg="#f1f8e9")
    filter_frame.pack(pady=5)
    tk.Checkbutton(filter_frame, text="فقط اکانت‌های در حال رسیدن", variable=var_only_pending, command=apply_filter, bg="#f1f8e9").pack(side="left", padx=10)
    tk.Checkbutton(filter_frame, text="فقط اکانت‌های رسیده", variable=var_only_reached, command=apply_filter, bg="#f1f8e9").pack(side="left", padx=10)

    columns = ("اکانت", "وضعیت", "روز باقی‌مانده", "امتیاز فعلی", "ارزش دلاری")
    tree = ttk.Treeview(top, columns=columns, show="headings")
    for col in columns:
        tree.heading(col, text=col, command=lambda c=col: sort_column(c, False))
        tree.column(col, width=140)
    tree.pack(fill="both", expand=True, padx=10, pady=10)

    style = ttk.Style()
    style.map("Treeview")
    tree.tag_configure("done", background="#c8e6c9")  # سبز
    tree.tag_configure("pending", background="#fff9c4")  # زرد

    lbl_summary = tk.Label(top, text="", bg="#f1f8e9", font=("Tahoma", 10))
    lbl_summary.pack(pady=5)

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT title, value FROM crowns")
    rows = cursor.fetchall()
    conn.close()

    apply_filter()









def show_forecast_window():
    def calculate_forecast(future_days=None):
        if future_days is None:
            try:
                future_days = int(entry_days.get())
            except ValueError:
                messagebox.showerror("خطا", "تعداد روز باید عدد باشد.")
                return

        update_forecast_treeview(future_days)

    def update_forecast_treeview(future_days):
        for i in forecast_tree.get_children():
            forecast_tree.delete(i)

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT title, value FROM crowns")
        rows = cursor.fetchall()
        conn.close()

        daily_growth = 1000
        threshold = 20000
        cumulative_dollars = 0

        # مرحله ۱: محاسبه مجموع دلاری اکانت‌هایی که همین الان بالای ۲۰۰۰۰ هستند
        current_dollars = 0
        passed_accounts = set()
        for title, value in rows:
            if value >= threshold:
                current_dollars += value / 10000
                passed_accounts.add(title)

        for day in range(1, future_days + 1):
            count = 0
            day_dollars = 0
            account_ids = []

            for title, current_value in rows:
                if title in passed_accounts:
                    continue
                future_value = current_value + daily_growth * day
                past_value = current_value + daily_growth * (day - 1)
                if past_value < threshold <= future_value:
                    count += 1
                    day_dollars += future_value / 10000
                    match = re.search(r"-(\d+)", title)
                    if match:
                        account_ids.append(match.group(1))

            cumulative_dollars += day_dollars
            total_with_current = cumulative_dollars + current_dollars
            forecast_tree.insert(
                "", tk.END,
                values=(
                    f"روز {day}",
                    count,
                    f"{cumulative_dollars:.2f}$",
                    f"{total_with_current:.2f}$",
                    ",".join(account_ids)
                )
            )

        lbl_forecast_sum.config(
            text=f"💰 مجموع دلاری تا پایان روز {future_days}: {cumulative_dollars:.2f}$\n"
                 f"💰 همراه با اکانت‌های فعلی: {cumulative_dollars + current_dollars:.2f}$"
        )

    # 👇 ساخت پنجره، باید در همین تابع باشه
    top = tk.Toplevel(root)
    top.title("پیش‌بینی رسیدن به ۲۰۰۰۰ امتیاز")
    top.geometry("780x550")
    top.configure(bg="#e1f5fe")

    default_days = 10

    tk.Label(top, text="تعداد روز آینده:", bg="#e1f5fe", font=("Tahoma", 10)).pack(pady=10)
    entry_days = tk.Entry(top, width=10)
    entry_days.insert(0, str(default_days))
    entry_days.pack(pady=5)

    tk.Button(top, text="محاسبه", command=lambda: calculate_forecast(int(entry_days.get())), bg="#0288d1", fg="white").pack(pady=10)

    lbl_result = tk.Label(top, text="", bg="#e1f5fe", font=("Tahoma", 10))
    lbl_result.pack(pady=10)

    # Treeview با ستون‌های کامل
    forecast_columns = (
        "روز",
        "تعداد اکانت‌ها",
        "ارزش دلاری تجمعی",
        "ارزش دلاری با احتساب اکانت‌های فعلی",
        "اکانت‌های روز"
    )
    forecast_tree = ttk.Treeview(top, columns=forecast_columns, show="headings")

    forecast_tree.column("روز", width=80)
    forecast_tree.column("تعداد اکانت‌ها", width=100)
    forecast_tree.column("ارزش دلاری تجمعی", width=120)
    forecast_tree.column("ارزش دلاری با احتساب اکانت‌های فعلی", width=170)
    forecast_tree.column("اکانت‌های روز", width=250)

    for col in forecast_columns:
        forecast_tree.heading(col, text=col)
        forecast_tree.heading(col, command=lambda col=col: sort_column(forecast_tree, col, False))

    forecast_tree.pack(pady=10, fill="both", expand=True)

    lbl_forecast_sum = tk.Label(top, text="", bg="#e1f5fe", font=("Tahoma", 10))
    lbl_forecast_sum.pack(pady=10)

    calculate_forecast(default_days)



def show_network_manager():
    def start_server():
        global server_socket, server_thread

        def handle_client(client_sock, addr):
            ip = addr[0]
            connected_time = datetime.now()
            clients_info[ip] = {
                "connected_time": connected_time,
                "accounts_running": 0,
                "db_data": {},
                "last_seen": connected_time
            }

            try:
                while True:
                    data = client_sock.recv(4096)
                    if not data:
                        break
                    decoded = json.loads(data.decode("utf-8"))

                    if decoded.get("type") == "connect":
                        clients_info[ip]["accounts_running"] = decoded.get("accounts_running", 0)
                        clients_info[ip]["db_data"] = decoded.get("db", {})
                        clients_info[ip]["last_seen"] = datetime.now()

            except Exception as e:
                print(f"❌ ارتباط با {ip} قطع شد: {e}")
            finally:
                client_sock.close()

        def server_loop():
            global server_socket
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.bind(("", SERVER_PORT))
            server_socket.listen(5)
            print(f"✅ سرور در حال گوش دادن روی پورت {SERVER_PORT}...")

            while True:
                client_sock, addr = server_socket.accept()
                threading.Thread(target=handle_client, args=(client_sock, addr), daemon=True).start()

        server_thread = threading.Thread(target=server_loop, daemon=True)
        server_thread.start()

    def connect_to_server():
        ip = entry_ip.get()
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.connect((ip, SERVER_PORT))

            # اطلاعات شبیه‌سازی‌شده
            sample_db = {"MuMu-23": {"value": 24500}, "MuMu-24": {"value": 19800}}
            data = {
                "type": "connect",
                "accounts_running": len(sample_db),
                "db": sample_db
            }
            s.send(json.dumps(data).encode("utf-8"))
            s.close()
            messagebox.showinfo("اتصال موفق", f"به سرور {ip} متصل شد و داده ارسال شد.")
        except Exception as e:
            messagebox.showerror("خطا در اتصال", str(e))

    def update_clients_tree():
        for row in tree.get_children():
            tree.delete(row)
        for ip, info in clients_info.items():
            duration = datetime.now() - info["connected_time"]
            tree.insert("", tk.END, values=(
                ip,
                info["connected_time"].strftime("%Y-%m-%d %H:%M:%S"),
                str(duration).split(".")[0],
                info["accounts_running"]
            ))
        root.after(60000, update_clients_tree)  # بروزرسانی هر دقیقه

    def show_client_db(event):
        selected = tree.selection()
        if not selected:
            return
        ip = tree.item(selected[0])['values'][0]
        db = clients_info[ip].get("db_data", {})

        win = tk.Toplevel(root)
        win.title(f"اطلاعات دیتابیس {ip}")
        cols = ("شبیه‌ساز", "امتیاز")
        sub_tree = ttk.Treeview(win, columns=cols, show="headings")
        for col in cols:
            sub_tree.heading(col, text=col)
            sub_tree.column(col, width=150)
        for title, data in db.items():
            sub_tree.insert("", tk.END, values=(title, data.get("value", 0)))
        sub_tree.pack(fill="both", expand=True, padx=10, pady=10)

    # ساخت فرم GUI
    root = tk.Toplevel()
    root.title("مدیریت اتصال شبکه")
    root.geometry("700x500")

    is_server = tk.BooleanVar()

    frame_top = tk.Frame(root)
    frame_top.pack(pady=10)

    tk.Checkbutton(frame_top, text="سرور هستم", variable=is_server).pack(side="left", padx=5)
    tk.Label(frame_top, text="IP سرور:").pack(side="left")
    entry_ip = tk.Entry(frame_top, width=20)
    entry_ip.pack(side="left", padx=5)
    tk.Button(frame_top, text="اتصال", command=connect_to_server).pack(side="left", padx=5)

    columns = ("IP", "زمان اتصال", "مدت اتصال", "تعداد اکانت‌ها")
    tree = ttk.Treeview(root, columns=columns, show="headings")
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=160)
    tree.pack(fill="both", expand=True, padx=10, pady=10)
    tree.bind("<Double-1>", show_client_db)

    if is_server.get():
        start_server()
        update_clients_tree()








    # شروع ساخت پنجره
    top = tk.Toplevel(root)
    top.title("پیش‌بینی رسیدن به ۲۰۰۰۰ امتیاز")
    top.geometry("780x550")  # بزرگ‌تر شد برای نمایش لیست اکانت‌ها
    top.configure(bg="#e1f5fe")

    default_days = 10

    tk.Label(top, text="تعداد روز آینده:", bg="#e1f5fe", font=("Tahoma", 10)).pack(pady=10)
    entry_days = tk.Entry(top, width=10)
    entry_days.insert(0, str(default_days))
    entry_days.pack(pady=5)

    tk.Button(top, text="محاسبه", command=lambda: calculate_forecast(int(entry_days.get())), bg="#0288d1", fg="white").pack(pady=10)

    lbl_result = tk.Label(top, text="", bg="#e1f5fe", font=("Tahoma", 10))
    lbl_result.pack(pady=10)

    # Treeview با ستون جدید
    forecast_columns = (
        "روز",
        "تعداد اکانت‌ها",
        "ارزش دلاری تجمعی",
        "ارزش دلاری با احتساب اکانت‌های فعلی",
        "اکانت‌های روز"
    )
    forecast_tree = ttk.Treeview(top, columns=forecast_columns, show="headings")

    # تعیین عرض بهینه برای هر ستون
    forecast_tree.column("روز", width=80)
    forecast_tree.column("تعداد اکانت‌ها", width=100)
    forecast_tree.column("ارزش دلاری تجمعی", width=120)
    forecast_tree.column("ارزش دلاری با احتساب اکانت‌های فعلی", width=170)
    forecast_tree.column("اکانت‌های روز", width=250)

    for col in forecast_columns:
        forecast_tree.heading(col, text=col)
        forecast_tree.heading(col, command=lambda col=col: sort_column(forecast_tree, col, False))

    forecast_tree.pack(pady=10, fill="both", expand=True)

    lbl_forecast_sum = tk.Label(top, text="", bg="#e1f5fe", font=("Tahoma", 10))
    lbl_forecast_sum.pack(pady=10)

    calculate_forecast(default_days)







# تابع مرتب‌سازی برای Treeviewهای مختلف
def sort_column(tree, col, reverse):
    l = [(tree.set(k, col), k) for k in tree.get_children("")]
    try:
        l.sort(key=lambda t: float(t[0].replace("$", "").replace("روز", "").strip()) if t[0].replace('.', '', 1).isdigit() or "$" in t[0] else t[0], reverse=reverse)
    except:
        l.sort(key=lambda t: t[0], reverse=reverse)

    for index, (val, k) in enumerate(l):
        tree.move(k, '', index)
    tree.heading(col, command=lambda: sort_column(tree, col, not reverse))



def show_daily_summary():
    top = tk.Toplevel(root)
    top.title("📅 گزارش روزانه")
    top.geometry("700x500")
    top.configure(bg="#e0f7fa")

    columns = ("تاریخ", "تعداد اکانت", "تعداد تبلیغات", "امتیاز افزایشی", "ارزش دلاری تبلیغات")
    tree = ttk.Treeview(top, columns=columns, show="headings")
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=130)
    tree.pack(fill="both", expand=True, padx=10, pady=10)

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT date FROM daily_stats ORDER BY date DESC")
    dates = [row[0] for row in cursor.fetchall()]

    summary = []
    for i in range(1, len(dates)):
        today = dates[i - 1]
        yesterday = dates[i]

        cursor.execute("SELECT title, value, adverts FROM daily_stats WHERE date=?", (today,))
        today_data = {row[0]: {"value": row[1], "adverts": row[2]} for row in cursor.fetchall()}

        cursor.execute("SELECT title, value FROM daily_stats WHERE date=?", (yesterday,))
        yesterday_data = {row[0]: row[1] for row in cursor.fetchall()}

        account_count = len(today_data)
        total_adverts = sum(d["adverts"] for d in today_data.values())
        total_increase = 0
        for title, data in today_data.items():
            prev_value = yesterday_data.get(title, 0)
            delta = data["value"] - prev_value
            if delta > 0:
                total_increase += delta

        earned_dollars = total_increase / 10000
        summary.append((today, account_count, total_adverts, total_increase, earned_dollars))

    conn.close()

    for row in summary:
        tree.insert("", tk.END, values=(row[0], row[1], row[2], row[3], f"{row[4]:.2f}$"))





def save_daily_stat(title, value, dollars, adverts):
    date_str = get_daily_date()
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO daily_stats (title, date, value, dollars, adverts)
        VALUES (?, ?, ?, ?, ?)
        ON CONFLICT(title, date) DO UPDATE SET
            value=excluded.value,
            dollars=excluded.dollars,
            adverts=excluded.adverts
    ''', (title, date_str, value, dollars, adverts))
    conn.commit()
    conn.close()

def update_treeview():
    for i in tree.get_children():
        tree.delete(i)
    for title, value, dollars, adverts in results:
        tree.insert("", tk.END, values=(title, value, f"{dollars:.2f}", adverts))

def start_processing():
    global running
    running = True
    try:
        limit = int(entry_limit.get()) if limit_filter_var.get() else None
        start = int(entry_range_start.get()) if range_filter_var.get() else None
        end = int(entry_range_end.get()) if range_filter_var.get() else None
    except ValueError:
        messagebox.showerror("خطا", "مقادیر وارد شده باید عدد صحیح باشند.")
        return
    threading.Thread(target=process_windows, args=(limit, start, end), daemon=True).start()

def stop_processing():
    global running
    running = False

def validate_numeric_input(P):
    return P.isdigit() or P == ""

def on_range_check():
    state = 'normal' if range_filter_var.get() else 'disabled'
    entry_range_start.config(state=state)
    entry_range_end.config(state=state)
    if not range_filter_var.get():
        entry_range_start.delete(0, tk.END)
        entry_range_end.delete(0, tk.END)

def on_limit_check():
    state = 'normal' if limit_filter_var.get() else 'disabled'
    entry_limit.config(state=state)
    if not limit_filter_var.get():
        entry_limit.delete(0, tk.END)

def show_database():
    def apply_filter():
        min_crowns = entry_filter_crowns.get()
        min_dollars = entry_filter_dollars.get()

        try:
            min_crowns_val = float(min_crowns) if min_crowns else 0
            min_dollars_val = float(min_dollars) if min_dollars else 0
        except ValueError:
            messagebox.showerror("خطا", "مقدار فیلتر باید عدد باشد.")
            return

        for i in tree_db.get_children():
            tree_db.delete(i)

        total_crowns = 0
        total_dollars = 0
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT title, value, dollars, adverts, last_updated FROM crowns")
        for index, row in enumerate(cursor.fetchall(), start=1):
            title, value, dollars, adverts, updated = row
            if value >= min_crowns_val and dollars >= min_dollars_val:
                tree_db.insert("", tk.END, values=(index, title, value, dollars, adverts, updated))
                total_crowns += value
                total_dollars += dollars
        conn.close()
        lbl_sum.config(text=f"👑 مجموع امتیاز: {total_crowns:.1f} | 💰 مجموع دلاری: {total_dollars:.2f}$")

    def delete_range_records():
        """
        حذف رکوردها در یک محدوده مشخص
        """
        # ایجاد پنجره برای دریافت محدوده
        range_window = tk.Toplevel(top)
        range_window.title("حذف محدوده رکوردها")
        range_window.geometry("400x200")
        range_window.configure(bg="#ffebee")
        range_window.resizable(False, False)

        # مرکزی کردن پنجره
        range_window.geometry("+%d+%d" % (top.winfo_rootx() + 50, top.winfo_rooty() + 50))

        # فریم اصلی
        main_frame = tk.Frame(range_window, bg="#ffebee", padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        # برچسب‌ها و ورودی‌ها
        tk.Label(main_frame, text="از ردیف:", bg="#ffebee", font=("Tahoma", 10, "bold")).grid(row=0, column=0, sticky="e", padx=5, pady=10)

        # تابع اعتبارسنجی برای ورود فقط اعداد
        def validate_number(text):
            return text.isdigit() or text == ""

        vcmd = (range_window.register(validate_number), '%P')

        start_row_entry = tk.Entry(main_frame, width=10, font=("Tahoma", 10), validate="key", validatecommand=vcmd)
        start_row_entry.grid(row=0, column=1, padx=5, pady=10)

        tk.Label(main_frame, text="تا ردیف:", bg="#ffebee", font=("Tahoma", 10, "bold")).grid(row=1, column=0, sticky="e", padx=5, pady=10)

        end_row_entry = tk.Entry(main_frame, width=10, font=("Tahoma", 10), validate="key", validatecommand=vcmd)
        end_row_entry.grid(row=1, column=1, padx=5, pady=10)

        # دکمه‌ها
        button_frame = tk.Frame(main_frame, bg="#ffebee")
        button_frame.grid(row=2, column=0, columnspan=2, pady=15)

        def confirm_delete():
            try:
                start_row = int(start_row_entry.get())
                end_row = int(end_row_entry.get())

                if start_row <= 0 or end_row <= 0:
                    messagebox.showerror("خطا", "شماره ردیف باید بزرگتر از صفر باشد.")
                    return

                if start_row > end_row:
                    messagebox.showerror("خطا", "شماره ردیف شروع باید کوچکتر یا مساوی شماره ردیف پایان باشد.")
                    return

                # دریافت تمام رکوردها از دیتابیس
                conn = sqlite3.connect(DB_PATH)
                cursor = conn.cursor()
                cursor.execute("SELECT title FROM crowns ORDER BY title")
                all_titles = [row[0] for row in cursor.fetchall()]

                # بررسی محدوده
                if start_row > len(all_titles):
                    messagebox.showerror("خطا", f"شماره ردیف شروع ({start_row}) بزرگتر از تعداد کل رکوردها ({len(all_titles)}) است.")
                    conn.close()
                    return

                # تنظیم محدوده پایان
                end_row = min(end_row, len(all_titles))

                # محاسبه ایندکس‌ها (با توجه به اینکه ردیف‌ها از 1 شروع می‌شوند)
                start_index = start_row - 1
                end_index = end_row - 1

                # انتخاب عناوین برای حذف
                titles_to_delete = all_titles[start_index:end_index+1]

                if not titles_to_delete:
                    messagebox.showinfo("اطلاعات", "هیچ رکوردی برای حذف یافت نشد.")
                    conn.close()
                    return

                # تأیید حذف
                if not messagebox.askyesno("تأیید حذف", f"آیا از حذف {len(titles_to_delete)} رکورد از ردیف {start_row} تا {end_row} اطمینان دارید؟ این عمل غیرقابل بازگشت است!"):
                    conn.close()
                    return

                # حذف رکوردها
                for title in titles_to_delete:
                    cursor.execute("DELETE FROM crowns WHERE title=?", (title,))
                    cursor.execute("DELETE FROM daily_stats WHERE title=?", (title,))

                conn.commit()
                conn.close()

                # بستن پنجره
                range_window.destroy()

                # بروزرسانی جدول
                apply_filter()

                messagebox.showinfo("اطلاعات", f"{len(titles_to_delete)} رکورد با موفقیت حذف شدند.")

            except ValueError:
                messagebox.showerror("خطا", "لطفاً شماره ردیف‌های معتبر وارد کنید.")
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در حذف رکوردها: {str(e)}")

        delete_button = tk.Button(button_frame, text="حذف", command=confirm_delete,
                                bg="#f44336", fg="white", font=("Tahoma", 10), bd=0, padx=10, pady=5)
        delete_button.pack(side="left", padx=5)

        cancel_button = tk.Button(button_frame, text="انصراف", command=range_window.destroy,
                                bg="#9e9e9e", fg="white", font=("Tahoma", 10), bd=0, padx=10, pady=5)
        cancel_button.pack(side="left", padx=5)

        # نمایش پنجره
        range_window.transient(top)
        range_window.grab_set()
        range_window.wait_window()

    def sort_column(col, reverse):
        l = [(tree_db.set(k, col), k) for k in tree_db.get_children("")]
        try:
            l.sort(key=lambda t: float(t[0]) if t[0] else 0, reverse=reverse)
        except:
            l.sort(reverse=reverse)
        for index, (val, k) in enumerate(l):
            tree_db.move(k, '', index)
        tree_db.heading(col, command=lambda: sort_column(col, not reverse))

    top = tk.Toplevel(root)
    top.title("نمایش اطلاعات دیتابیس")
    top.geometry("850x600")
    top.configure(bg='#fce4ec')

    columns = ("ردیف", "title", "value", "dollars", "adverts", "updated")
    tree_db = ttk.Treeview(top, columns=columns, show="headings")
    headings_fa = {
        "ردیف": "ردیف",
        "title": "نام شبیه‌ساز",
        "value": "امتیاز",
        "dollars": "ارزش دلاری",
        "adverts": "تعداد تبلیغات",
        "updated": "زمان بروزرسانی"
    }
    for col in columns:
        tree_db.heading(col, text=headings_fa[col], command=lambda _col=col: sort_column(_col, False))
        tree_db.column(col, width=120)

    scrollbar = ttk.Scrollbar(top, orient="vertical", command=tree_db.yview)
    tree_db.configure(yscrollcommand=scrollbar.set)
    tree_db.pack(side="top", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    filter_frame = tk.Frame(top, bg="#fce4ec")
    filter_frame.pack(fill="x", padx=10, pady=5)

    tk.Label(filter_frame, text="حداقل امتیاز:", bg="#fce4ec").pack(side="left")
    entry_filter_crowns = tk.Entry(filter_frame, width=10)
    entry_filter_crowns.pack(side="left", padx=5)

    tk.Label(filter_frame, text="حداقل دلار:", bg="#fce4ec").pack(side="left")
    entry_filter_dollars = tk.Entry(filter_frame, width=10)
    entry_filter_dollars.pack(side="left", padx=5)

    tk.Button(filter_frame, text="اعمال فیلتر", command=apply_filter, bg="#8e24aa", fg="white").pack(side="left", padx=10)

    # دکمه‌های عملیات
    button_frame = tk.Frame(top, bg="#fce4ec")
    button_frame.pack(fill="x", padx=10, pady=5)

    # دکمه حذف محدوده
    btn_delete_range = tk.Button(button_frame, text="🗑️ حذف محدوده رکوردها", command=delete_range_records, bg="#ff5722", fg="white")
    btn_delete_range.pack(side="left", padx=5)

    # دکمه ویرایش رکورد
    btn_edit = tk.Button(button_frame, text="✏️ ویرایش رکورد انتخاب شده", command=lambda: edit_selected_record(tree_db), bg="#4caf50", fg="white")
    btn_edit.pack(side="left", padx=5)

    def edit_selected_record(tree):
        selected = tree.selection()
        if not selected:
            messagebox.showinfo("اطلاعات", "لطفاً یک رکورد را انتخاب کنید.")
            return

        item = tree.item(selected[0])
        title = item['values'][1]
        edit_record(title)
        top.destroy()
        show_database()

    lbl_sum = tk.Label(top, text="", bg="#fce4ec", font=("Tahoma", 10))
    lbl_sum.pack(pady=5)

    def on_double_click(event):
        selected = tree_db.selection()
        if selected:
            item = tree_db.item(selected[0])
            title = item['values'][1]
            edit_record(title)
            top.destroy()
            show_database()

    tree_db.bind("<Double-1>", on_double_click)

    apply_filter()

def clear_database():
    if messagebox.askyesno("تأیید", "آیا از حذف همه داده‌ها مطمئن هستید؟"):
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM crowns")
        conn.commit()
        conn.close()
        messagebox.showinfo("پاکسازی شد", "همه داده‌ها حذف شدند.")












# --- ساخت پنجره اصلی ---
root = tk.Tk()
root.title("🪄 استخراج Crowns و تعداد تبلیغات MuMu Player")
root.geometry("800x650")
root.configure(bg='#ffe0b2')

vcmd = (root.register(validate_numeric_input), '%P')

frame_top = tk.LabelFrame(root, text="تنظیمات", bg='#ffe0b2', fg='#bf360c', font=("Tahoma", 10, "bold"))
frame_top.pack(pady=10, padx=10, fill="x")
tk.Label(frame_top, text="بازه اسکن (دقیقه):", bg="#ffe0b2").grid(row=5, column=0, padx=5, pady=2)
entry_interval = tk.Entry(frame_top, width=5, validate='key', validatecommand=vcmd)
entry_interval.grid(row=5, column=1, padx=5)
entry_interval.insert(0, "5")  # مقدار پیش‌فرض

btn_auto_scan = tk.Button(frame_top, text="🕒 شروع اسکن خودکار", command=toggle_auto_scan, bg="#0097a7", fg="white", width=20)
btn_auto_scan.grid(row=5, column=2, columnspan=2, padx=5)

limit_filter_var = tk.BooleanVar()
range_filter_var = tk.BooleanVar()

check_limit = tk.Checkbutton(frame_top, text="فعال‌سازی تعداد محدود", variable=limit_filter_var, bg='#ffe0b2', command=on_limit_check)
check_limit.grid(row=0, column=0, sticky='w', padx=5)
entry_limit = tk.Entry(frame_top, width=5, validate='key', validatecommand=vcmd, state='disabled')
entry_limit.grid(row=0, column=1, padx=5, pady=5)

check_range = tk.Checkbutton(frame_top, text="فعال‌سازی بازه شبیه‌ساز", variable=range_filter_var, bg='#ffe0b2', command=on_range_check)
check_range.grid(row=1, column=0, columnspan=2, sticky='w', pady=2)

entry_range_start = tk.Entry(frame_top, width=5, validate='key', validatecommand=vcmd, state='disabled')
entry_range_end = tk.Entry(frame_top, width=5, validate='key', validatecommand=vcmd, state='disabled')
entry_range_start.grid(row=2, column=1, padx=5)
entry_range_end.grid(row=2, column=3, padx=5)
label_range = tk.Label(frame_top, text="محدوده از → تا (مثلاً 92 تا 97):", bg='#ffe0b2')
label_range.grid(row=2, column=0, columnspan=1, sticky='w')
btn_forecast = tk.Button(frame_top, text="📊 پیش‌بینی امتیاز", command=show_forecast_window, bg='#00796b', fg='white', width=15)
btn_forecast.grid(row=4, column=0, pady=5, padx=5)
btn_network = tk.Button(frame_top, text="🌐 اتصال شبکه", command=show_network_manager, bg='#6a1b9a', fg='white', width=15)
btn_network.grid(row=4, column=1, pady=5, padx=5)

btn_adv_forecast = tk.Button(
    frame_top, text="📊 تحلیل پیشرفته",
    command=lambda: AccountAdvancedForecast(root),  # root پاس داده بشه
    bg="#00897b", fg="white", width=15
)

btn_adv_forecast.grid(row=5, column=1, pady=5, padx=5)



btn_start = tk.Button(frame_top, text="شروع", command=start_processing, bg='#43a047', fg='white', width=10)

btn_start.grid(row=3, column=0, pady=10, padx=5)
btn_stop = tk.Button(frame_top, text="توقف", command=stop_processing, bg='#e53935', fg='white', width=10)
btn_stop.grid(row=3, column=1, pady=10, padx=5)
btn_show = tk.Button(frame_top, text="📂 مشاهده دیتابیس", command=show_database, bg='#3949ab', fg='white', width=15)
btn_show.grid(row=3, column=2, pady=10, padx=5)
btn_clear = tk.Button(frame_top, text="🗑️ پاکسازی دیتابیس", command=clear_database, bg='#fb8c00', fg='white', width=15)
btn_clear.grid(row=3, column=3, pady=10, padx=5)



btn_summary = tk.Button(frame_top, text="📅 آمار روزانه", command=show_daily_summary, bg='#00796b', fg='white', width=15)
btn_summary.grid(row=4, column=2, pady=5, padx=5)





# افزودن دکمه به GUI اصلی (مثلاً در frame_top)
btn_forecast2d = tk.Button(frame_top, text="📊 پیش‌بینی 2 دلار", command=show_forecast_to_2dollars, bg="#6a1b9a", fg="white", width=15)
btn_forecast2d.grid(row=4, column=3, pady=5, padx=5)
total_label = tk.Label(root, text="🔍 تعداد کل شبیه‌سازها: 0", bg='#ffe0b2', font=("Tahoma", 9))
total_label.pack()

total_crowns_label = tk.Label(root, text="👑 مجموع Crownها: 0.0", bg='#ffe0b2', font=("Tahoma", 9))
total_crowns_label.pack()

total_dollar_label = tk.Label(root, text="💰 مجموع دلاری: 0.00$", bg='#ffe0b2', font=("Tahoma", 9))
total_dollar_label.pack()

frame_tree = tk.Frame(root)
frame_tree.pack(fill="both", expand=True, padx=10, pady=5)

columns = ("پنجره", "Crowns", "دلار", "تعداد تبلیغات")
tree = ttk.Treeview(frame_tree, columns=columns, show="headings", height=15)
tree.heading("پنجره", text="نام پنجره")
tree.heading("Crowns", text="امتیاز")
tree.heading("دلار", text="دلار")
tree.heading("تعداد تبلیغات", text="تعداد تبلیغات")

style = ttk.Style()
style.theme_use("clam")
style.configure("Treeview", background="#ffffff", foreground="black", rowheight=25, fieldbackground="#fbe9e7")
style.map('Treeview', background=[('selected', '#ffab91')])

scrollbar = ttk.Scrollbar(frame_tree, orient="vertical", command=tree.yview)
tree.configure(yscrollcommand=scrollbar.set)
scrollbar.pack(side="right", fill="y")
tree.pack(fill="both", expand=True)

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime

DB_PATH = "crowns.db"

class AccountAdvancedForecast(tk.Toplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("📊 تحلیل پیشرفته اکانت‌ها")
        self.geometry("1300x600")

        self.future_days = tk.IntVar(value=5)
        self.filter_value = tk.StringVar()

        self.create_widgets()
        self.load_data()

    def create_widgets(self):
        control_frame = tk.Frame(self)
        control_frame.pack(fill="x", padx=10, pady=5)

        tk.Label(control_frame, text="روز آینده:").pack(side="left")
        tk.Entry(control_frame, textvariable=self.future_days, width=5).pack(side="left", padx=5)
        tk.Button(control_frame, text="محاسبه", command=self.load_data).pack(side="left", padx=5)

        tk.Label(control_frame, text="فیلتر دلار >=:").pack(side="left", padx=10)
        tk.Entry(control_frame, textvariable=self.filter_value, width=5).pack(side="left")
        tk.Button(control_frame, text="اعمال فیلتر", command=self.apply_filter).pack(side="left", padx=5)

        columns = ("name", "score", "dollar", "reached2", "reached3", "reached4", "future", "d2", "d3", "d4")
        self.tree = ttk.Treeview(self, columns=columns, show="headings")

        headings = [
            "نام", "امتیاز", "$ فعلی", "رسیده به $2", "رسیده به $3", "رسیده به $4",
            f"ارزش در {self.future_days.get()} روز", "روز تا $2", "روز تا $3", "روز تا $4"
        ]
        for col, title in zip(columns, headings):
            self.tree.heading(col, text=title, command=lambda _col=col: self.sort_tree(_col, False))
            self.tree.column(col, anchor="center", width=100)
        self.tree.pack(expand=True, fill="both", padx=10, pady=5)

        style = ttk.Style()
        style.theme_use("clam")
        style.configure("Treeview", background="#ffffff", foreground="black",
                        rowheight=25, fieldbackground="#f9fbe7")
        style.map('Treeview', background=[('selected', '#c8e6c9')])

        self.total_label = tk.Label(self, text="", font=("Arial", 10))
        self.total_label.pack()
        self.future_label = tk.Label(self, text="", font=("Arial", 10, "bold"))
        self.future_label.pack()

    def fetch_data(self):
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT title, value FROM crowns")
        rows = cursor.fetchall()
        conn.close()
        return rows

    def load_data(self):
        self.tree.delete(*self.tree.get_children())
        rows = self.fetch_data()
        total2 = 0
        total_future = 0
        days = self.future_days.get()

        for title, score in rows:
            dollar = score / 10000
            future = (score + 1000 * days) / 10000
            r2 = "رسیده" if dollar >= 2 else ""
            r3 = "رسیده" if dollar >= 3 else ""
            r4 = "رسیده" if dollar >= 4 else ""
            d2 = max(0, (20000 - score + 999) // 1000) if score < 20000 else 0
            d3 = max(0, (30000 - score + 999) // 1000) if score < 30000 else 0
            d4 = max(0, (40000 - score + 999) // 1000) if score < 40000 else 0

            total2 += dollar if dollar >= 2 else 0
            total_future += future

            item = self.tree.insert("", "end", values=(
                title, int(score), f"{dollar:.2f}", r2, r3, r4, f"{future:.2f}", d2, d3, d4
            ))
            if dollar >= 4:
                self.tree.item(item, tags=("four",))
            elif dollar >= 3:
                self.tree.item(item, tags=("three",))
            elif dollar >= 2:
                self.tree.item(item, tags=("two",))

        self.tree.tag_configure("two", background="#dcedc8")
        self.tree.tag_configure("three", background="#ffe082")
        self.tree.tag_configure("four", background="#ef9a9a")

        self.total_label.config(text=f"✅ مجموع اکانت‌های بالای $2: ${total2:.2f}")
        self.future_label.config(text=f"⏳ مجموع ارزش پیش‌بینی‌شده {days} روز بعد: ${total_future:.2f}")

    def sort_tree(self, col, reverse):
        l = [(self.tree.set(k, col), k) for k in self.tree.get_children("")]
        try:
            l.sort(key=lambda t: float(t[0].replace("$", "")), reverse=reverse)
        except:
            l.sort(reverse=reverse)
        for index, (val, k) in enumerate(l):
            self.tree.move(k, "", index)
        self.tree.heading(col, command=lambda: self.sort_tree(col, not reverse))

    def apply_filter(self):
        try:
            threshold = float(self.filter_value.get())
        except ValueError:
            messagebox.showerror("خطا", "عدد معتبری وارد کنید.")
            return
        for i in self.tree.get_children():
            self.tree.delete(i)
        rows = self.fetch_data()
        days = self.future_days.get()
        for title, score in rows:
            dollar = score / 10000
            if dollar < threshold:
                continue
            future = (score + 1000 * days) / 10000
            r2 = "رسیده" if dollar >= 2 else ""
            r3 = "رسیده" if dollar >= 3 else ""
            r4 = "رسیده" if dollar >= 4 else ""
            d2 = max(0, (20000 - score + 999) // 1000) if score < 20000 else 0
            d3 = max(0, (30000 - score + 999) // 1000) if score < 30000 else 0
            d4 = max(0, (40000 - score + 999) // 1000) if score < 40000 else 0

            item = self.tree.insert("", "end", values=(
                title, int(score), f"{dollar:.2f}", r2, r3, r4, f"{future:.2f}", d2, d3, d4
            ))
            if dollar >= 4:
                self.tree.item(item, tags=("four",))
            elif dollar >= 3:
                self.tree.item(item, tags=("three",))
            elif dollar >= 2:
                self.tree.item(item, tags=("two",))

        self.tree.tag_configure("two", background="#dcedc8")
        self.tree.tag_configure("three", background="#ffe082")
        self.tree.tag_configure("four", background="#ef9a9a")

        self.total_label.config(text=f"✅ مجموع اکانت‌های بالای $2: ${total2:.2f}")
        self.future_label.config(text=f"⏳ مجموع ارزش پیش‌بینی‌شده {days} روز بعد: ${total_future:.2f}")

    def sort_tree(self, col, reverse):
        l = [(self.tree.set(k, col), k) for k in self.tree.get_children("")]
        try:
            l.sort(key=lambda t: float(t[0].replace("$", "")), reverse=reverse)
        except:
            l.sort(reverse=reverse)
        for index, (val, k) in enumerate(l):
            self.tree.move(k, "", index)
        self.tree.heading(col, command=lambda: self.sort_tree(col, not reverse))

    def apply_filter(self):
        try:
            threshold = float(self.filter_value.get())
        except ValueError:
            messagebox.showerror("خطا", "عدد معتبری وارد کنید.")
            return
        for i in self.tree.get_children():
            self.tree.delete(i)
        rows = self.fetch_data()
        days = self.future_days.get()
        for title, score in rows:
            dollar = score / 10000
            if dollar < threshold:
                continue
            future = (score + 1000 * days) / 10000
            r2 = "رسیده" if dollar >= 2 else ""
            r3 = "رسیده" if dollar >= 3 else ""
            r4 = "رسیده" if dollar >= 4 else ""
            d2 = max(0, (20000 - score + 999) // 1000) if score < 20000 else 0
            d3 = max(0, (30000 - score + 999) // 1000) if score < 30000 else 0
            d4 = max(0, (40000 - score + 999) // 1000) if score < 40000 else 0

            item = self.tree.insert("", "end", values=(
                title, int(score), f"{dollar:.2f}", r2, r3, r4, f"{future:.2f}", d2, d3, d4
            ))
            if dollar >= 4:
                self.tree.item(item, tags=("four",))
            elif dollar >= 3:
                self.tree.item(item, tags=("three",))
            elif dollar >= 2:
                self.tree.item(item, tags=("two",))









root.mainloop()
