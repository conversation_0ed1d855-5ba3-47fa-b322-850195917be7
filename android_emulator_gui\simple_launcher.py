#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple Launcher for Android Emulator Farm
راه‌انداز ساده مزرعه شبیه‌ساز اندروید
"""

import os
import sys

def show_menu():
    """نمایش منوی اصلی"""
    print("\n" + "="*70)
    print("🤖 Android Emulator Farm - انتخاب نوع شبیه‌ساز")
    print("="*70)
    print()
    print("1. 🔥 شبیه‌سازهای واقعی اندروید")
    print("   ✅ نصب واقعی APK ها")
    print("   ✅ اجرای واقعی Kepithor Rewards")
    print("   ✅ استفاده از Android SDK")
    print("   ✅ عملکرد 100% واقعی")
    print("   ⚠️ نیاز به Android SDK")
    print("   ⚠️ مصرف بالای منابع")
    print("   📊 توصیه: 1-10 دستگاه")
    print()
    print("2. ⚡ شبیه‌سازهای سریع (فیک)")
    print("   ✅ راه‌اندازی سریع")
    print("   ✅ مصرف کم منابع")
    print("   ✅ تا 80 دستگاه همزمان")
    print("   ✅ رابط گرافیکی زیبا")
    print("   ⚠️ شبیه‌سازی فیک")
    print("   ⚠️ عدم نصب واقعی APK")
    print("   📊 توصیه: تست و نمایش")
    print()
    print("3. 🧪 تست شبیه‌ساز واقعی (یک دستگاه)")
    print("4. 🔧 بررسی سیستم")
    print("5. 🚪 خروج")
    print("="*70)

def check_system():
    """بررسی سیستم"""
    print("\n🔍 بررسی سیستم...")
    
    try:
        import psutil
        
        cpu_count = psutil.cpu_count()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('.')
        
        print(f"\n💻 مشخصات سیستم:")
        print(f"   CPU: {cpu_count} هسته")
        print(f"   RAM: {memory.total / (1024**3):.1f} GB (آزاد: {memory.available / (1024**3):.1f} GB)")
        print(f"   فضای دیسک: {disk.free / (1024**3):.1f} GB آزاد")
        
        # بررسی Android SDK
        sdk_paths = [
            os.path.expanduser("~/Android/Sdk"),
            os.path.expanduser("~/AppData/Local/Android/Sdk"),
            "C:/Android/Sdk"
        ]
        
        sdk_found = False
        for path in sdk_paths:
            if os.path.exists(path):
                print(f"   Android SDK: ✅ یافت شد ({path})")
                sdk_found = True
                break
        
        if not sdk_found:
            print(f"   Android SDK: ❌ یافت نشد")
        
        # بررسی فایل XAPK
        xapk_path = "Kepithor Rewards_159_APKPure.xapk"
        if os.path.exists(xapk_path):
            size_mb = os.path.getsize(xapk_path) / (1024*1024)
            print(f"   Kepithor XAPK: ✅ یافت شد ({size_mb:.1f} MB)")
        else:
            print(f"   Kepithor XAPK: ❌ یافت نشد")
        
        print(f"\n📊 توصیه‌ها:")
        print(f"   • شبیه‌سازهای واقعی: {min(10, cpu_count // 2)} دستگاه")
        print(f"   • شبیه‌سازهای فیک: {min(80, cpu_count * 10)} دستگاه")
        
        if memory.available < 4 * (1024**3):
            print("   ⚠️ هشدار: RAM کم برای شبیه‌سازهای واقعی")
        
        if cpu_count < 4:
            print("   ⚠️ هشدار: CPU کم برای چندین شبیه‌ساز")
        
    except ImportError:
        print("❌ نمی‌توان سیستم را بررسی کرد (psutil نصب نیست)")

def launch_real_emulators():
    """راه‌اندازی شبیه‌سازهای واقعی"""
    print("\n🔥 راه‌اندازی شبیه‌سازهای واقعی...")
    
    try:
        # بررسی Android SDK
        sdk_paths = [
            os.path.expanduser("~/Android/Sdk"),
            os.path.expanduser("~/AppData/Local/Android/Sdk"),
            "C:/Android/Sdk"
        ]
        
        sdk_found = False
        for path in sdk_paths:
            if os.path.exists(path):
                sdk_found = True
                break
        
        if not sdk_found:
            print("⚠️ Android SDK یافت نشد!")
            choice = input("آیا می‌خواهید Android SDK دانلود شود؟ (y/n): ").strip().lower()
            if choice != 'y':
                return
        
        print("🚀 راه‌اندازی GUI شبیه‌سازهای واقعی...")
        
        # import و اجرای GUI واقعی
        try:
            from main_gui import EmulatorFarmGUI
            gui = EmulatorFarmGUI(use_real_emulators=True)
            gui.run()
        except ImportError as e:
            print(f"❌ خطا در import: {e}")
            print("در حال تلاش برای اجرای مستقیم...")
            
            # اجرای مستقیم
            os.system("python main_gui.py")
        
    except Exception as e:
        print(f"❌ خطا در راه‌اندازی شبیه‌سازهای واقعی: {e}")

def launch_fake_emulators():
    """راه‌اندازی شبیه‌سازهای فیک"""
    print("\n⚡ راه‌اندازی شبیه‌سازهای فیک...")
    
    try:
        print("🚀 راه‌اندازی GUI شبیه‌سازهای فیک...")
        
        # import و اجرای GUI فیک
        try:
            from main_gui import EmulatorFarmGUI
            gui = EmulatorFarmGUI(use_real_emulators=False)
            gui.run()
        except ImportError as e:
            print(f"❌ خطا در import: {e}")
            print("در حال تلاش برای اجرای مستقیم...")
            
            # اجرای مستقیم
            os.system("python main_gui.py")
        
    except Exception as e:
        print(f"❌ خطا در راه‌اندازی شبیه‌سازهای فیک: {e}")

def test_real_emulator():
    """تست شبیه‌ساز واقعی"""
    print("\n🧪 تست شبیه‌ساز واقعی...")
    
    print("⚠️ هشدار: این تست 5-10 دقیقه طول می‌کشد!")
    choice = input("ادامه می‌دهید؟ (y/n): ").strip().lower()
    
    if choice == 'y':
        try:
            print("🚀 اجرای تست...")
            os.system("python test_real_emulator.py")
        except Exception as e:
            print(f"❌ خطا در تست: {e}")

def main():
    """تابع اصلی"""
    print("🤖 Android Emulator Farm - Simple Launcher")
    print("راه‌انداز ساده مزرعه شبیه‌ساز اندروید")
    
    while True:
        try:
            show_menu()
            choice = input("\nانتخاب کنید (1-5): ").strip()
            
            if choice == "1":
                launch_real_emulators()
            
            elif choice == "2":
                launch_fake_emulators()
            
            elif choice == "3":
                test_real_emulator()
            
            elif choice == "4":
                check_system()
            
            elif choice == "5":
                print("👋 خداحافظ!")
                break
            
            else:
                print("❌ انتخاب نامعتبر!")
            
            input("\nPress Enter to continue...")
            
        except KeyboardInterrupt:
            print("\n👋 خداحافظ!")
            break
        except Exception as e:
            print(f"❌ خطا: {e}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    main()
