#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
XAPK Extractor
استخراج فایل APK از فرمت XAPK
"""

import os
import zipfile
import json
import shutil

def extract_xapk(xapk_path, output_dir="extracted_xapk"):
    """Extract XAPK file and find the main APK"""
    try:
        print(f"📦 Extracting XAPK: {xapk_path}")
        
        # Create output directory
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)
        os.makedirs(output_dir)
        
        # Extract XAPK (it's actually a ZIP file)
        with zipfile.ZipFile(xapk_path, 'r') as zip_ref:
            zip_ref.extractall(output_dir)
        
        print(f"✅ XAPK extracted to: {output_dir}")
        
        # List extracted files
        extracted_files = []
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                file_path = os.path.join(root, file)
                extracted_files.append(file_path)
                print(f"📄 Found: {file_path}")
        
        # Find APK files
        apk_files = [f for f in extracted_files if f.endswith('.apk')]
        
        if apk_files:
            print(f"\n🎯 Found {len(apk_files)} APK file(s):")
            for apk in apk_files:
                print(f"📱 {apk}")
            
            # Return the main APK (usually the largest one)
            main_apk = max(apk_files, key=os.path.getsize)
            print(f"\n🚀 Main APK: {main_apk}")
            return main_apk
        else:
            print("❌ No APK files found in XAPK")
            return None
            
    except Exception as e:
        print(f"❌ Error extracting XAPK: {e}")
        return None

def main():
    """Main function"""
    xapk_file = "Kepithor Rewards_159_APKPure.xapk"
    
    if not os.path.exists(xapk_file):
        print(f"❌ XAPK file not found: {xapk_file}")
        return
    
    # Extract XAPK
    main_apk = extract_xapk(xapk_file)
    
    if main_apk:
        print(f"\n🎉 Success! Main APK extracted: {main_apk}")
        print(f"Now you can analyze it with:")
        print(f"python apk_analyzer.py \"{main_apk}\"")
    else:
        print("\n❌ Failed to extract APK from XAPK")

if __name__ == "__main__":
    main()
