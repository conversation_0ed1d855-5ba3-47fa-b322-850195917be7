#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Real Android Emulator Manager
مدیریت شبیه‌سازهای واقعی اندروید
"""

import os
import sys
import json
import time
import threading
import zipfile
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import subprocess

from real_android_emulator import RealAndroidEmulator

class RealEmulatorManager:
    """مدیریت شبیه‌سازهای واقعی اندروید"""
    
    def __init__(self, max_devices=10):
        """راه‌اندازی مدیر"""
        self.max_devices = max_devices
        self.emulators = {}
        self.running_emulators = {}
        
        # آمار
        self.stats = {
            'total_devices': 0,
            'total_emulators': 0,
            'running_emulators': 0,
            'installed_apps': 0,
            'active_kepithor': 0,
            'cpu_usage': 0,
            'memory_usage': 0,
            'memory_available_gb': 0,
            'memory_used_gb': 0
        }
        
        print(f"🏭 Real Emulator Manager initialized for {max_devices} devices")
    
    def create_emulator(self, device_id):
        """ایجاد شبیه‌ساز جدید"""
        try:
            emulator = RealAndroidEmulator(device_id)
            self.emulators[device_id] = emulator
            self.stats['total_devices'] += 1
            self.stats['total_emulators'] += 1
            
            print(f"📱 Real emulator {device_id} created")
            return emulator
            
        except Exception as e:
            print(f"❌ Error creating emulator {device_id}: {e}")
            return None
    
    def start_emulator(self, device_id):
        """راه‌اندازی شبیه‌ساز"""
        try:
            if device_id not in self.emulators:
                emulator = self.create_emulator(device_id)
                if not emulator:
                    return False
            
            emulator = self.emulators[device_id]
            
            if emulator.start_emulator():
                self.running_emulators[device_id] = emulator
                self.stats['running_emulators'] += 1
                return True
            else:
                return False
                
        except Exception as e:
            print(f"❌ Error starting emulator {device_id}: {e}")
            return False
    
    def stop_emulator(self, device_id):
        """توقف شبیه‌ساز"""
        try:
            if device_id in self.running_emulators:
                emulator = self.running_emulators[device_id]
                emulator.shutdown()
                del self.running_emulators[device_id]
                self.stats['running_emulators'] -= 1
                return True
            else:
                return False
                
        except Exception as e:
            print(f"❌ Error stopping emulator {device_id}: {e}")
            return False
    
    def start_all_emulators(self, progress_callback=None):
        """راه‌اندازی همه شبیه‌سازها"""
        print(f"🚀 Starting {self.max_devices} real emulators...")
        
        successful = 0
        failed = 0
        
        # راه‌اندازی تدریجی برای جلوگیری از overload
        for device_id in range(1, self.max_devices + 1):
            print(f"📱 Starting real emulator {device_id}...")
            
            try:
                if self.start_emulator(device_id):
                    successful += 1
                    print(f"✅ Real emulator {device_id} started")
                else:
                    failed += 1
                    print(f"❌ Real emulator {device_id} failed")
                
                # به‌روزرسانی progress
                if progress_callback:
                    progress = ((successful + failed) / self.max_devices) * 100
                    progress_callback(progress, f"Starting emulator {device_id}")
                
                # تأخیر بین راه‌اندازی دستگاه‌ها
                if device_id < self.max_devices:
                    print("⏳ Waiting 30 seconds before starting next emulator...")
                    time.sleep(30)
                    
            except Exception as e:
                failed += 1
                print(f"❌ Emulator {device_id} error: {e}")
        
        print(f"🎉 Startup complete: {successful} successful, {failed} failed")
        return successful
    
    def stop_all_emulators(self, progress_callback=None):
        """توقف همه شبیه‌سازها"""
        print("🛑 Stopping all real emulators...")
        
        emulator_ids = list(self.running_emulators.keys())
        stopped = 0
        
        for device_id in emulator_ids:
            try:
                self.stop_emulator(device_id)
                stopped += 1
                print(f"✅ Real emulator {device_id} stopped")
                
                # به‌روزرسانی progress
                if progress_callback:
                    progress = (stopped / len(emulator_ids)) * 100
                    progress_callback(progress, f"Stopping emulator {device_id}")
                    
            except Exception as e:
                print(f"❌ Emulator {device_id} stop error: {e}")
        
        print(f"🎉 {stopped}/{len(emulator_ids)} real emulators stopped")
        return stopped
    
    def install_apk_on_all(self, apk_path, progress_callback=None):
        """نصب APK روی همه شبیه‌سازها"""
        if not os.path.exists(apk_path):
            print(f"❌ APK file not found: {apk_path}")
            return 0
        
        # بررسی فرمت فایل
        if apk_path.lower().endswith('.xapk'):
            # استخراج XAPK
            extracted_apk = self.extract_xapk(apk_path)
            if not extracted_apk:
                return 0
            apk_path = extracted_apk
        
        print(f"📦 Installing APK on {len(self.running_emulators)} real emulators...")
        
        successful = 0
        
        for device_id, emulator in self.running_emulators.items():
            try:
                print(f"📦 Installing APK on real emulator {device_id}...")
                if emulator.install_apk(apk_path):
                    successful += 1
                    print(f"✅ APK installed on real emulator {device_id}")
                    
                    # به‌روزرسانی progress
                    if progress_callback:
                        progress = (successful / len(self.running_emulators)) * 100
                        progress_callback(progress, f"Installing on emulator {device_id}")
                else:
                    print(f"❌ APK installation failed on real emulator {device_id}")
                    
            except Exception as e:
                print(f"❌ Emulator {device_id} install error: {e}")
        
        self.stats['installed_apps'] = successful
        print(f"🎉 APK installed on {successful}/{len(self.running_emulators)} real emulators")
        return successful
    
    def extract_xapk(self, xapk_path):
        """استخراج فایل XAPK"""
        try:
            print(f"📦 Extracting XAPK: {xapk_path}")
            
            extract_dir = Path("temp_xapk_extraction")
            extract_dir.mkdir(exist_ok=True)
            
            with zipfile.ZipFile(xapk_path, 'r') as xapk_zip:
                xapk_zip.extractall(extract_dir)
            
            # جستجوی فایل APK اصلی
            apk_files = list(extract_dir.glob("*.apk"))
            
            if not apk_files:
                print("❌ No APK files found in XAPK")
                return None
            
            # انتخاب بزرگترین APK (معمولاً APK اصلی)
            main_apk = max(apk_files, key=lambda x: x.stat().st_size)
            
            print(f"✅ Main APK extracted: {main_apk}")
            return str(main_apk)
            
        except Exception as e:
            print(f"❌ XAPK extraction error: {e}")
            return None
    
    def launch_kepithor_on_all(self, progress_callback=None):
        """اجرای Kepithor روی همه شبیه‌سازها"""
        print(f"🚀 Launching Kepithor on {len(self.running_emulators)} real emulators...")
        
        successful = 0
        package_name = 'com.KepithorStudios.KKTFaucet'
        
        for device_id, emulator in self.running_emulators.items():
            try:
                print(f"🚀 Launching Kepithor on real emulator {device_id}...")
                if emulator.launch_app(package_name):
                    successful += 1
                    print(f"✅ Kepithor launched on real emulator {device_id}")
                    
                    # به‌روزرسانی progress
                    if progress_callback:
                        progress = (successful / len(self.running_emulators)) * 100
                        progress_callback(progress, f"Launching on emulator {device_id}")
                else:
                    print(f"❌ Kepithor launch failed on real emulator {device_id}")
                    
            except Exception as e:
                print(f"❌ Emulator {device_id} launch error: {e}")
        
        self.stats['active_kepithor'] = successful
        print(f"🎉 Kepithor launched on {successful}/{len(self.running_emulators)} real emulators")
        return successful
    
    def stop_kepithor_on_all(self, progress_callback=None):
        """توقف Kepithor روی همه شبیه‌سازها"""
        print(f"🛑 Stopping Kepithor on {len(self.running_emulators)} real emulators...")
        
        successful = 0
        package_name = 'com.KepithorStudios.KKTFaucet'
        
        for device_id, emulator in self.running_emulators.items():
            try:
                if emulator.stop_app(package_name):
                    successful += 1
                    print(f"✅ Kepithor stopped on real emulator {device_id}")
                    
                    # به‌روزرسانی progress
                    if progress_callback:
                        progress = (successful / len(self.running_emulators)) * 100
                        progress_callback(progress, f"Stopping on emulator {device_id}")
                        
            except Exception as e:
                print(f"❌ Emulator {device_id} stop error: {e}")
        
        self.stats['active_kepithor'] = 0
        print(f"🎉 Kepithor stopped on {successful}/{len(self.running_emulators)} real emulators")
        return successful
    
    def take_screenshots_all(self):
        """گرفتن اسکرین‌شات از همه شبیه‌سازها"""
        print(f"📸 Taking screenshots from {len(self.running_emulators)} real emulators...")
        
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)
        
        successful = 0
        
        for device_id, emulator in self.running_emulators.items():
            try:
                screenshot_path = screenshots_dir / f"emulator_{device_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                
                if emulator.take_screenshot(str(screenshot_path)):
                    successful += 1
                    print(f"📸 Screenshot taken from emulator {device_id}")
                    
            except Exception as e:
                print(f"❌ Screenshot error for emulator {device_id}: {e}")
        
        print(f"🎉 {successful} screenshots taken")
        return successful
    
    def get_system_stats(self):
        """دریافت آمار سیستم"""
        try:
            import psutil
            
            # آمار CPU و RAM
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            self.stats.update({
                'running_emulators': len(self.running_emulators),
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'memory_used_gb': memory.used / (1024**3)
            })
            
            return self.stats
            
        except Exception as e:
            print(f"❌ Error getting system stats: {e}")
            return self.stats
    
    def get_emulators_info(self):
        """دریافت اطلاعات همه شبیه‌سازها"""
        emulators_info = []
        
        for device_id, emulator in self.emulators.items():
            info = emulator.get_device_info()
            emulators_info.append(info)
        
        return emulators_info
    
    def print_status(self):
        """چاپ وضعیت فعلی"""
        stats = self.get_system_stats()
        
        print("\n" + "="*80)
        print("📊 REAL ANDROID EMULATOR FARM STATUS")
        print("="*80)
        print(f"📱 Total Emulators: {self.stats['total_emulators']}")
        print(f"🟢 Running Emulators: {stats['running_emulators']}")
        print(f"📦 Emulators with APK: {self.stats['installed_apps']}")
        print(f"💰 Active Kepithor: {stats['active_kepithor']}")
        print(f"💾 Memory Usage: {stats['memory_usage']:.1f}% ({stats['memory_used_gb']:.1f} GB)")
        print(f"🔥 CPU Usage: {stats['cpu_usage']:.1f}%")
        print(f"💽 Available Memory: {stats['memory_available_gb']:.1f} GB")
        print("="*80)
        
        # نمایش اطلاعات شبیه‌سازها
        if self.running_emulators:
            print("\n🤖 Running Real Emulators:")
            for device_id, emulator in list(self.running_emulators.items())[:10]:
                print(f"  Emulator {device_id}: {emulator.adb_device_name} (Port: {emulator.port})")
            
            if len(self.running_emulators) > 10:
                print(f"  ... and {len(self.running_emulators) - 10} more emulators")

def main():
    """تابع اصلی برای تست"""
    print("🏭 Real Android Emulator Farm")
    print("=" * 50)
    
    # ایجاد مدیر
    manager = RealEmulatorManager(max_devices=2)  # تست با 2 شبیه‌ساز
    
    try:
        # راه‌اندازی شبیه‌سازها
        print("1. Starting real emulators...")
        successful = manager.start_all_emulators()
        
        if successful > 0:
            # نمایش آمار
            manager.print_status()
            
            # نصب APK
            apk_path = "Kepithor Rewards_159_APKPure.xapk"
            if os.path.exists(apk_path):
                print("\n2. Installing Kepithor XAPK...")
                manager.install_apk_on_all(apk_path)
                
                print("\n3. Launching Kepithor...")
                manager.launch_kepithor_on_all()
                
                print("\n4. Taking screenshots...")
                manager.take_screenshots_all()
            
            # نمایش آمار نهایی
            print("\n5. Final status:")
            manager.print_status()
            
            # نگه داشتن برای مشاهده
            input("\nPress Enter to shutdown all emulators...")
        
        # توقف همه شبیه‌سازها
        manager.stop_all_emulators()
        
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        manager.stop_all_emulators()

if __name__ == "__main__":
    main()
