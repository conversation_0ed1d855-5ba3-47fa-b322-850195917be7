#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Android Device Emulator with GUI
شبیه‌ساز دستگاه اندروید با رابط گرافیکی
"""

import os
import sys
import json
import time
import uuid
import random
import threading
import sqlite3
import zipfile
import requests
from datetime import datetime
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageDraw
import subprocess

class AndroidDevice:
    """شبیه‌سازی دستگاه اندروید با رابط گرافیکی"""
    
    def __init__(self, device_id, ip_address=None):
        """راه‌اندازی دستگاه"""
        self.device_id = device_id
        self.ip_address = ip_address or self.generate_fake_ip()
        self.port = 5555 + device_id
        
        # مشخصات دستگاه
        self.device_specs = self.generate_device_specs()
        
        # مسیرها
        self.base_dir = Path(f"devices/device_{device_id}")
        self.apps_dir = self.base_dir / "apps"
        self.data_dir = self.base_dir / "data"
        
        # وضعیت دستگاه
        self.is_running = False
        self.installed_apps = {}
        self.running_apps = {}
        self.current_app = None
        
        # رابط گرافیکی
        self.gui_window = None
        self.screen_canvas = None
        self.screen_width = 360
        self.screen_height = 640
        
        # ایجاد ساختار
        self.create_device_structure()
        self.init_database()
    
    def generate_fake_ip(self):
        """تولید IP فیک برای هر دستگاه"""
        # تولید IP های مختلف برای هر دستگاه
        base_ips = [
            "192.168.1.",
            "192.168.0.", 
            "10.0.0.",
            "172.16.0.",
            "192.168.100.",
            "192.168.50."
        ]
        
        base = random.choice(base_ips)
        last_octet = (self.device_id % 200) + 10  # از 10 تا 210
        return f"{base}{last_octet}"
    
    def generate_device_specs(self):
        """تولید مشخصات دستگاه"""
        devices = [
            {"model": "SM-G973F", "brand": "Samsung", "name": "Galaxy S10"},
            {"model": "Pixel 4", "brand": "Google", "name": "Pixel 4"},
            {"model": "OnePlus 8", "brand": "OnePlus", "name": "OnePlus 8"},
            {"model": "Mi 10", "brand": "Xiaomi", "name": "Mi 10"},
            {"model": "Galaxy A51", "brand": "Samsung", "name": "Galaxy A51"},
            {"model": "Redmi Note 9", "brand": "Xiaomi", "name": "Redmi Note 9"},
        ]
        
        device = random.choice(devices)
        android_version = random.choice(["10", "11", "12", "13"])
        
        return {
            "model": device["model"],
            "brand": device["brand"],
            "name": device["name"],
            "android_version": android_version,
            "api_level": {"10": 29, "11": 30, "12": 31, "13": 33}[android_version],
            "device_id": str(uuid.uuid4()),
            "android_id": ''.join(random.choices('0123456789abcdef', k=16)),
            "imei": ''.join(random.choices('0123456789', k=15)),
            "serial": f"emulator-{self.port}",
            "build_id": f"RKQ1.{random.randint(200000, 210000)}.{random.randint(100, 999)}",
            "screen_width": self.screen_width,
            "screen_height": self.screen_height,
            "density": random.choice([2.0, 2.75, 3.0]),
            "ram_mb": random.choice([4096, 6144, 8192]),
            "storage_gb": random.choice([64, 128, 256])
        }
    
    def create_device_structure(self):
        """ایجاد ساختار فولدرها"""
        try:
            self.base_dir.mkdir(parents=True, exist_ok=True)
            self.apps_dir.mkdir(exist_ok=True)
            self.data_dir.mkdir(exist_ok=True)
            
            print(f"📁 Device {self.device_id} structure created")
        except Exception as e:
            print(f"❌ Error creating structure: {e}")
    
    def init_database(self):
        """راه‌اندازی پایگاه داده"""
        try:
            db_path = self.base_dir / "device.db"
            self.conn = sqlite3.connect(str(db_path), check_same_thread=False)
            self.cursor = self.conn.cursor()
            
            # جدول برنامه‌ها
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS apps (
                    package_name TEXT PRIMARY KEY,
                    app_name TEXT,
                    version_name TEXT,
                    install_date TEXT,
                    is_running BOOLEAN DEFAULT FALSE
                )
            """)
            
            # جدول تنظیمات
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT
                )
            """)
            
            # ذخیره مشخصات
            for key, value in self.device_specs.items():
                self.cursor.execute(
                    "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)",
                    (key, str(value))
                )
            
            # ذخیره IP
            self.cursor.execute(
                "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)",
                ("ip_address", self.ip_address)
            )
            
            self.conn.commit()
            print(f"💾 Device {self.device_id} database initialized")
            
        except Exception as e:
            print(f"❌ Database error: {e}")
    
    def create_gui_window(self):
        """ایجاد پنجره گرافیکی دستگاه"""
        try:
            self.gui_window = tk.Toplevel()
            self.gui_window.title(f"📱 Device {self.device_id} - {self.device_specs['name']}")
            self.gui_window.geometry("400x700")
            self.gui_window.configure(bg="#2c2c2c")
            
            # فریم اطلاعات دستگاه
            info_frame = tk.Frame(self.gui_window, bg="#2c2c2c", height=60)
            info_frame.pack(fill=tk.X, padx=5, pady=5)
            info_frame.pack_propagate(False)
            
            # اطلاعات دستگاه
            device_info = f"📱 {self.device_specs['name']} | 🌐 IP: {self.ip_address}"
            info_label = tk.Label(
                info_frame,
                text=device_info,
                bg="#2c2c2c",
                fg="white",
                font=("Arial", 9)
            )
            info_label.pack(pady=5)
            
            # وضعیت
            self.status_label = tk.Label(
                info_frame,
                text="🔴 Offline",
                bg="#2c2c2c",
                fg="#ff4444",
                font=("Arial", 9, "bold")
            )
            self.status_label.pack()
            
            # صفحه نمایش
            screen_frame = tk.Frame(self.gui_window, bg="#000000", relief=tk.RAISED, bd=2)
            screen_frame.pack(padx=10, pady=5)
            
            self.screen_canvas = tk.Canvas(
                screen_frame,
                width=self.screen_width,
                height=self.screen_height,
                bg="#000000",
                highlightthickness=0
            )
            self.screen_canvas.pack()
            
            # دکمه‌های کنترل
            control_frame = tk.Frame(self.gui_window, bg="#2c2c2c")
            control_frame.pack(fill=tk.X, padx=10, pady=5)
            
            # دکمه Home
            self.home_btn = tk.Button(
                control_frame,
                text="🏠",
                command=self.go_home,
                font=("Arial", 12),
                width=8,
                bg="#444444",
                fg="white",
                relief=tk.FLAT
            )
            self.home_btn.pack(side=tk.LEFT, padx=2)
            
            # دکمه Back
            self.back_btn = tk.Button(
                control_frame,
                text="⬅️",
                command=self.go_back,
                font=("Arial", 12),
                width=8,
                bg="#444444",
                fg="white",
                relief=tk.FLAT
            )
            self.back_btn.pack(side=tk.LEFT, padx=2)
            
            # دکمه Menu
            self.menu_btn = tk.Button(
                control_frame,
                text="☰",
                command=self.show_menu,
                font=("Arial", 12),
                width=8,
                bg="#444444",
                fg="white",
                relief=tk.FLAT
            )
            self.menu_btn.pack(side=tk.LEFT, padx=2)
            
            # نمایش صفحه اصلی
            self.show_home_screen()
            
            # بستن پنجره
            self.gui_window.protocol("WM_DELETE_WINDOW", self.close_gui)
            
            print(f"🖥️ GUI window created for device {self.device_id}")
            
        except Exception as e:
            print(f"❌ GUI creation error: {e}")
    
    def show_home_screen(self):
        """نمایش صفحه اصلی اندروید"""
        try:
            self.screen_canvas.delete("all")
            
            # پس‌زمینه
            self.screen_canvas.create_rectangle(
                0, 0, self.screen_width, self.screen_height,
                fill="#1a1a1a", outline=""
            )
            
            # نوار بالا
            self.screen_canvas.create_rectangle(
                0, 0, self.screen_width, 40,
                fill="#333333", outline=""
            )
            
            # ساعت
            current_time = datetime.now().strftime("%H:%M")
            self.screen_canvas.create_text(
                30, 20, text=current_time,
                fill="white", font=("Arial", 12)
            )
            
            # باتری
            self.screen_canvas.create_text(
                self.screen_width - 30, 20, text="🔋 85%",
                fill="white", font=("Arial", 10)
            )
            
            # آیکون‌های برنامه
            self.draw_app_icons()
            
            # نوار پایین
            self.screen_canvas.create_rectangle(
                0, self.screen_height - 60, self.screen_width, self.screen_height,
                fill="#333333", outline=""
            )
            
            # دکمه‌های پایین
            self.draw_navigation_buttons()
            
        except Exception as e:
            print(f"❌ Home screen error: {e}")
    
    def draw_app_icons(self):
        """رسم آیکون‌های برنامه‌ها"""
        try:
            # آیکون‌های پیش‌فرض
            default_apps = [
                {"name": "Settings", "icon": "⚙️", "x": 60, "y": 100},
                {"name": "Browser", "icon": "🌐", "x": 150, "y": 100},
                {"name": "Camera", "icon": "📷", "x": 240, "y": 100},
                {"name": "Gallery", "icon": "🖼️", "x": 300, "y": 100},
            ]
            
            for app in default_apps:
                # پس‌زمینه آیکون
                self.screen_canvas.create_oval(
                    app["x"] - 25, app["y"] - 25,
                    app["x"] + 25, app["y"] + 25,
                    fill="#444444", outline="#666666"
                )
                
                # آیکون
                self.screen_canvas.create_text(
                    app["x"], app["y"], text=app["icon"],
                    fill="white", font=("Arial", 20)
                )
                
                # نام برنامه
                self.screen_canvas.create_text(
                    app["x"], app["y"] + 40, text=app["name"],
                    fill="white", font=("Arial", 8)
                )
            
            # آیکون Kepithor (اگر نصب شده)
            if 'com.KepithorStudios.KKTFaucet' in self.installed_apps:
                kepithor_x, kepithor_y = 180, 200
                
                # پس‌زمینه آیکون Kepithor
                self.screen_canvas.create_oval(
                    kepithor_x - 30, kepithor_y - 30,
                    kepithor_x + 30, kepithor_y + 30,
                    fill="#4CAF50", outline="#66BB6A", width=2
                )
                
                # آیکون Kepithor
                self.screen_canvas.create_text(
                    kepithor_x, kepithor_y, text="💰",
                    fill="white", font=("Arial", 24)
                )
                
                # نام برنامه
                self.screen_canvas.create_text(
                    kepithor_x, kepithor_y + 50, text="Kepithor",
                    fill="white", font=("Arial", 10, "bold")
                )
                
                # قابلیت کلیک
                self.screen_canvas.tag_bind(
                    self.screen_canvas.create_oval(
                        kepithor_x - 30, kepithor_y - 30,
                        kepithor_x + 30, kepithor_y + 30,
                        fill="", outline=""
                    ),
                    "<Button-1>",
                    lambda e: self.launch_kepithor()
                )
            
        except Exception as e:
            print(f"❌ App icons error: {e}")
    
    def draw_navigation_buttons(self):
        """رسم دکمه‌های ناوبری"""
        try:
            button_y = self.screen_height - 30
            
            # دکمه Back
            self.screen_canvas.create_text(
                60, button_y, text="◀",
                fill="white", font=("Arial", 16)
            )
            
            # دکمه Home
            self.screen_canvas.create_text(
                180, button_y, text="⚫",
                fill="white", font=("Arial", 16)
            )
            
            # دکمه Recent
            self.screen_canvas.create_text(
                300, button_y, text="▢",
                fill="white", font=("Arial", 16)
            )
            
        except Exception as e:
            print(f"❌ Navigation buttons error: {e}")
    
    def start_device(self):
        """راه‌اندازی دستگاه"""
        try:
            if self.is_running:
                return True
            
            print(f"🚀 Starting device {self.device_id}...")
            
            # ایجاد GUI
            self.create_gui_window()
            
            # تغییر وضعیت
            self.is_running = True
            
            if self.status_label:
                self.status_label.config(text="🟢 Online", fg="#44ff44")
            
            print(f"✅ Device {self.device_id} started successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error starting device {self.device_id}: {e}")
            return False
    
    def install_apk(self, apk_path):
        """نصب APK"""
        try:
            if not os.path.exists(apk_path):
                print(f"❌ APK not found: {apk_path}")
                return False
            
            print(f"📦 Installing APK on device {self.device_id}...")
            
            # استخراج اطلاعات APK
            app_info = self.extract_apk_info(apk_path)
            if not app_info:
                return False
            
            # کپی APK
            app_dir = self.apps_dir / app_info['package_name']
            app_dir.mkdir(exist_ok=True)
            
            import shutil
            shutil.copy2(apk_path, app_dir / "base.apk")
            
            # ثبت در پایگاه داده
            self.cursor.execute("""
                INSERT OR REPLACE INTO apps 
                (package_name, app_name, version_name, install_date)
                VALUES (?, ?, ?, ?)
            """, (
                app_info['package_name'],
                app_info['app_name'],
                app_info['version_name'],
                datetime.now().isoformat()
            ))
            
            self.conn.commit()
            self.installed_apps[app_info['package_name']] = app_info
            
            # به‌روزرسانی صفحه اصلی
            if self.screen_canvas:
                self.show_home_screen()
            
            print(f"✅ APK installed on device {self.device_id}")
            return True
            
        except Exception as e:
            print(f"❌ Install error: {e}")
            return False
    
    def extract_apk_info(self, apk_path):
        """استخراج اطلاعات APK"""
        try:
            with zipfile.ZipFile(apk_path, 'r') as apk_zip:
                if 'AndroidManifest.xml' in apk_zip.namelist():
                    manifest_data = apk_zip.read('AndroidManifest.xml')
                    manifest_str = str(manifest_data)
                    
                    # تشخیص Kepithor
                    if 'KepithorStudios' in manifest_str or 'KKTFaucet' in manifest_str:
                        return {
                            'package_name': 'com.KepithorStudios.KKTFaucet',
                            'app_name': 'Kepithor Rewards',
                            'version_name': '1.59'
                        }
            
            return {
                'package_name': f'com.example.app_{random.randint(1000, 9999)}',
                'app_name': 'Unknown App',
                'version_name': '1.0'
            }
            
        except Exception as e:
            print(f"❌ APK extraction error: {e}")
            return None
    
    def launch_kepithor(self):
        """اجرای Kepithor"""
        try:
            if 'com.KepithorStudios.KKTFaucet' not in self.installed_apps:
                messagebox.showwarning("هشدار", "Kepithor نصب نشده است!")
                return False
            
            print(f"🚀 Launching Kepithor on device {self.device_id}")
            
            # نمایش صفحه Kepithor
            self.show_kepithor_screen()
            
            # ثبت در running apps
            self.running_apps['com.KepithorStudios.KKTFaucet'] = True
            self.current_app = 'com.KepithorStudios.KKTFaucet'
            
            return True
            
        except Exception as e:
            print(f"❌ Launch error: {e}")
            return False
    
    def show_kepithor_screen(self):
        """نمایش صفحه Kepithor"""
        try:
            self.screen_canvas.delete("all")
            
            # پس‌زمینه
            self.screen_canvas.create_rectangle(
                0, 0, self.screen_width, self.screen_height,
                fill="#1e3a8a", outline=""
            )
            
            # نوار بالا
            self.screen_canvas.create_rectangle(
                0, 0, self.screen_width, 50,
                fill="#1e40af", outline=""
            )
            
            # عنوان
            self.screen_canvas.create_text(
                self.screen_width // 2, 25, text="💰 Kepithor Rewards",
                fill="white", font=("Arial", 14, "bold")
            )
            
            # موجودی
            balance = random.randint(5000, 15000)
            self.screen_canvas.create_text(
                self.screen_width // 2, 100, text=f"موجودی: {balance:,} امتیاز",
                fill="white", font=("Arial", 16, "bold")
            )
            
            # دکمه تماشای تبلیغ
            watch_ad_btn = self.screen_canvas.create_rectangle(
                50, 150, self.screen_width - 50, 200,
                fill="#10b981", outline="#059669", width=2
            )
            
            self.screen_canvas.create_text(
                self.screen_width // 2, 175, text="📺 تماشای تبلیغ",
                fill="white", font=("Arial", 12, "bold")
            )
            
            # دکمه دریافت جایزه
            claim_btn = self.screen_canvas.create_rectangle(
                50, 220, self.screen_width - 50, 270,
                fill="#f59e0b", outline="#d97706", width=2
            )
            
            self.screen_canvas.create_text(
                self.screen_width // 2, 245, text="🎁 دریافت جایزه",
                fill="white", font=("Arial", 12, "bold")
            )
            
            # آمار
            self.screen_canvas.create_text(
                self.screen_width // 2, 320, text="📊 آمار امروز:",
                fill="white", font=("Arial", 12, "bold")
            )
            
            ads_watched = random.randint(10, 50)
            self.screen_canvas.create_text(
                self.screen_width // 2, 350, text=f"تبلیغات مشاهده شده: {ads_watched}",
                fill="white", font=("Arial", 10)
            )
            
            earned_today = random.randint(1000, 5000)
            self.screen_canvas.create_text(
                self.screen_width // 2, 370, text=f"امتیاز کسب شده: {earned_today:,}",
                fill="white", font=("Arial", 10)
            )
            
            # شبیه‌سازی فعالیت
            self.simulate_kepithor_activity()
            
        except Exception as e:
            print(f"❌ Kepithor screen error: {e}")
    
    def simulate_kepithor_activity(self):
        """شبیه‌سازی فعالیت Kepithor"""
        try:
            # شبیه‌سازی تماشای تبلیغ هر 30-60 ثانیه
            if self.current_app == 'com.KepithorStudios.KKTFaucet' and self.is_running:
                # نمایش پیام فعالیت
                activity_text = random.choice([
                    "📺 در حال تماشای تبلیغ...",
                    "💰 امتیاز دریافت شد!",
                    "🎁 جایزه روزانه دریافت شد!",
                    "⏳ در انتظار تبلیغ بعدی..."
                ])
                
                # نمایش در پایین صفحه
                if self.screen_canvas:
                    self.screen_canvas.create_rectangle(
                        10, self.screen_height - 80, self.screen_width - 10, self.screen_height - 40,
                        fill="#374151", outline="#4b5563"
                    )
                    
                    self.screen_canvas.create_text(
                        self.screen_width // 2, self.screen_height - 60, text=activity_text,
                        fill="white", font=("Arial", 9)
                    )
                
                # برنامه‌ریزی فعالیت بعدی
                next_activity = random.randint(30000, 60000)  # 30-60 ثانیه
                if self.gui_window:
                    self.gui_window.after(next_activity, self.simulate_kepithor_activity)
                
        except Exception as e:
            print(f"❌ Activity simulation error: {e}")
    
    def go_home(self):
        """بازگشت به صفحه اصلی"""
        self.current_app = None
        self.show_home_screen()
    
    def go_back(self):
        """دکمه بازگشت"""
        if self.current_app:
            self.go_home()
    
    def show_menu(self):
        """نمایش منو"""
        messagebox.showinfo("منو", f"منوی دستگاه {self.device_id}")
    
    def close_gui(self):
        """بستن پنجره GUI"""
        try:
            if self.gui_window:
                self.gui_window.destroy()
                self.gui_window = None
            
            self.is_running = False
            print(f"🔌 Device {self.device_id} GUI closed")
            
        except Exception as e:
            print(f"❌ Close GUI error: {e}")
    
    def shutdown(self):
        """خاموش کردن دستگاه"""
        try:
            print(f"🔌 Shutting down device {self.device_id}...")
            
            # بستن GUI
            self.close_gui()
            
            # بستن پایگاه داده
            if hasattr(self, 'conn'):
                self.conn.close()
            
            self.is_running = False
            print(f"✅ Device {self.device_id} shutdown complete")
            
        except Exception as e:
            print(f"❌ Shutdown error: {e}")
    
    def get_device_info(self):
        """دریافت اطلاعات دستگاه"""
        return {
            'device_id': self.device_id,
            'ip_address': self.ip_address,
            'port': self.port,
            'is_running': self.is_running,
            'specs': self.device_specs,
            'installed_apps': len(self.installed_apps),
            'running_apps': len(self.running_apps),
            'current_app': self.current_app
        }
