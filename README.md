# OCR برنامه (OCR Application)

یک برنامه گرافیکی ساده برای تشخیص متن از تصاویر با استفاده از پایتون و Tkinter.

## ویژگی‌ها

- رابط کاربری گرافیکی با دو تب: "اسکن و نمایش" و "تنظیمات"
- امکان انتخاب تصویر از سیستم
- نمایش نتیجه تشخیص متن
- تنظیمات زبان و موتور تشخیص

## نیازمندی‌ها

- Python 3.6 یا بالاتر
- Tkinter (معمولاً همراه با نصب پایتون ارائه می‌شود)

## نحوه اجرا

برای اجرای برنامه، دستور زیر را در ترمینال وارد کنید:

```
python ocr_app.py
```

## توسعه آینده

- افزودن پشتیبانی از موتور OCR واقعی مانند Tesseract
- ذخیره و بازیابی تنظیمات
- پردازش تصویر قبل از OCR
- امکان ذخیره نتایج OCR

---

# OCR Application

A simple graphical application for Optical Character Recognition (OCR) using Python and Tkinter.

## Features

- Graphical user interface with two tabs: "Scan and Display" and "Settings"
- Ability to select an image from the system
- Display OCR results
- Language and OCR engine settings

## Requirements

- Python 3.6 or higher
- Tkinter (usually comes with Python installation)

## How to Run

To run the application, enter the following command in the terminal:

```
python ocr_app.py
```

## Future Development

- Add support for a real OCR engine like Tesseract
- Save and load settings
- Image preprocessing before OCR
- Ability to save OCR results
