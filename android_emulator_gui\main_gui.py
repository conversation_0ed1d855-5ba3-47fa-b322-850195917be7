#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main GUI for Android Emulator Farm
رابط گرافیکی اصلی مزرعه شبیه‌ساز اندروید
"""

import os
import sys
import json
import time
import threading
from datetime import datetime
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk

from emulator_farm import EmulatorFarm

class EmulatorFarmGUI:
    """رابط گرافیکی مزرعه شبیه‌ساز"""
    
    def __init__(self):
        """راه‌اندازی رابط گرافیکی"""
        self.farm = EmulatorFarm(max_devices=80)
        self.setup_gui()
        self.update_stats_timer()
        
    def setup_gui(self):
        """ایجاد رابط گرافیکی"""
        self.root = tk.Tk()
        self.root.title("🏭 Android Emulator Farm - 80 Devices")
        self.root.geometry("1200x800")
        self.root.configure(bg="#1a1a1a")
        
        # استایل
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        
        # فریم اصلی
        main_frame = tk.Frame(self.root, bg="#1a1a1a")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان
        title_label = tk.Label(
            main_frame,
            text="🏭 مزرعه شبیه‌ساز اندروید - مدیریت 80 دستگاه همزمان",
            font=("Arial", 18, "bold"),
            bg="#1a1a1a",
            fg="#ffffff"
        )
        title_label.pack(pady=10)
        
        # فریم کنترل‌ها
        self.create_control_panel(main_frame)
        
        # فریم آمار
        self.create_stats_panel(main_frame)
        
        # فریم لیست دستگاه‌ها
        self.create_devices_panel(main_frame)
        
        # نوار وضعیت
        self.create_status_bar(main_frame)
    
    def create_control_panel(self, parent):
        """ایجاد پنل کنترل"""
        control_frame = tk.LabelFrame(
            parent,
            text="🎮 کنترل مزرعه",
            font=("Arial", 12, "bold"),
            bg="#2a2a2a",
            fg="#ffffff",
            relief=tk.RAISED,
            bd=2
        )
        control_frame.pack(fill=tk.X, pady=5)
        
        # ردیف اول دکمه‌ها
        row1_frame = tk.Frame(control_frame, bg="#2a2a2a")
        row1_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # دکمه راه‌اندازی همه
        self.start_all_btn = tk.Button(
            row1_frame,
            text="🚀 راه‌اندازی همه دستگاه‌ها (80)",
            command=self.start_all_devices,
            font=("Arial", 11, "bold"),
            bg="#4CAF50",
            fg="white",
            width=30,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.start_all_btn.pack(side=tk.LEFT, padx=5)
        
        # دکمه توقف همه
        self.stop_all_btn = tk.Button(
            row1_frame,
            text="🛑 توقف همه دستگاه‌ها",
            command=self.stop_all_devices,
            font=("Arial", 11, "bold"),
            bg="#f44336",
            fg="white",
            width=30,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.stop_all_btn.pack(side=tk.LEFT, padx=5)
        
        # دکمه نصب APK/XAPK
        self.install_apk_btn = tk.Button(
            row1_frame,
            text="📦 نصب Kepithor (APK/XAPK)",
            command=self.install_apk,
            font=("Arial", 11, "bold"),
            bg="#FF9800",
            fg="white",
            width=30,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.install_apk_btn.pack(side=tk.LEFT, padx=5)
        
        # ردیف دوم دکمه‌ها
        row2_frame = tk.Frame(control_frame, bg="#2a2a2a")
        row2_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # دکمه اجرای Kepithor
        self.launch_kepithor_btn = tk.Button(
            row2_frame,
            text="💰 اجرای Kepithor روی همه",
            command=self.launch_kepithor,
            font=("Arial", 11, "bold"),
            bg="#2196F3",
            fg="white",
            width=30,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.launch_kepithor_btn.pack(side=tk.LEFT, padx=5)
        
        # دکمه تنظیمات
        self.settings_btn = tk.Button(
            row2_frame,
            text="⚙️ تنظیمات مزرعه",
            command=self.show_settings,
            font=("Arial", 11, "bold"),
            bg="#9C27B0",
            fg="white",
            width=30,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.settings_btn.pack(side=tk.LEFT, padx=5)
        
        # دکمه گزارش
        self.report_btn = tk.Button(
            row2_frame,
            text="📊 گزارش عملکرد",
            command=self.show_report,
            font=("Arial", 11, "bold"),
            bg="#607D8B",
            fg="white",
            width=30,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.report_btn.pack(side=tk.LEFT, padx=5)
        
        # نوار پیشرفت
        self.progress_frame = tk.Frame(control_frame, bg="#2a2a2a")
        self.progress_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=400,
            mode='determinate'
        )
        self.progress_bar.pack(side=tk.LEFT, padx=5)
        
        self.progress_label = tk.Label(
            self.progress_frame,
            text="آماده",
            bg="#2a2a2a",
            fg="#ffffff",
            font=("Arial", 10)
        )
        self.progress_label.pack(side=tk.LEFT, padx=10)
    
    def create_stats_panel(self, parent):
        """ایجاد پنل آمار"""
        stats_frame = tk.LabelFrame(
            parent,
            text="📊 آمار سیستم",
            font=("Arial", 12, "bold"),
            bg="#2a2a2a",
            fg="#ffffff",
            relief=tk.RAISED,
            bd=2
        )
        stats_frame.pack(fill=tk.X, pady=5)
        
        # فریم آمار
        stats_content = tk.Frame(stats_frame, bg="#2a2a2a")
        stats_content.pack(fill=tk.X, padx=10, pady=5)
        
        # ستون اول
        col1_frame = tk.Frame(stats_content, bg="#2a2a2a")
        col1_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.total_devices_label = tk.Label(
            col1_frame,
            text="📱 کل دستگاه‌ها: 0",
            bg="#2a2a2a",
            fg="#ffffff",
            font=("Arial", 11, "bold")
        )
        self.total_devices_label.pack(anchor=tk.W, pady=2)
        
        self.running_devices_label = tk.Label(
            col1_frame,
            text="🟢 دستگاه‌های فعال: 0",
            bg="#2a2a2a",
            fg="#4CAF50",
            font=("Arial", 11, "bold")
        )
        self.running_devices_label.pack(anchor=tk.W, pady=2)
        
        # ستون دوم
        col2_frame = tk.Frame(stats_content, bg="#2a2a2a")
        col2_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.installed_apps_label = tk.Label(
            col2_frame,
            text="📦 APK نصب شده: 0",
            bg="#2a2a2a",
            fg="#FF9800",
            font=("Arial", 11, "bold")
        )
        self.installed_apps_label.pack(anchor=tk.W, pady=2)
        
        self.active_kepithor_label = tk.Label(
            col2_frame,
            text="💰 Kepithor فعال: 0",
            bg="#2a2a2a",
            fg="#2196F3",
            font=("Arial", 11, "bold")
        )
        self.active_kepithor_label.pack(anchor=tk.W, pady=2)
        
        # ستون سوم
        col3_frame = tk.Frame(stats_content, bg="#2a2a2a")
        col3_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.memory_label = tk.Label(
            col3_frame,
            text="💾 حافظه: 0%",
            bg="#2a2a2a",
            fg="#ffffff",
            font=("Arial", 11, "bold")
        )
        self.memory_label.pack(anchor=tk.W, pady=2)
        
        self.cpu_label = tk.Label(
            col3_frame,
            text="🔥 CPU: 0%",
            bg="#2a2a2a",
            fg="#ffffff",
            font=("Arial", 11, "bold")
        )
        self.cpu_label.pack(anchor=tk.W, pady=2)
    
    def create_devices_panel(self, parent):
        """ایجاد پنل لیست دستگاه‌ها"""
        devices_frame = tk.LabelFrame(
            parent,
            text="📱 لیست دستگاه‌ها",
            font=("Arial", 12, "bold"),
            bg="#2a2a2a",
            fg="#ffffff",
            relief=tk.RAISED,
            bd=2
        )
        devices_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # جدول دستگاه‌ها
        columns = ("ID", "IP Address", "Status", "Model", "Kepithor", "Memory")
        self.devices_tree = ttk.Treeview(
            devices_frame,
            columns=columns,
            show="headings",
            height=15
        )
        
        # تنظیم ستون‌ها
        for col in columns:
            self.devices_tree.heading(col, text=col)
            if col == "ID":
                self.devices_tree.column(col, width=50)
            elif col == "IP Address":
                self.devices_tree.column(col, width=120)
            elif col == "Status":
                self.devices_tree.column(col, width=80)
            elif col == "Model":
                self.devices_tree.column(col, width=150)
            elif col == "Kepithor":
                self.devices_tree.column(col, width=80)
            else:
                self.devices_tree.column(col, width=100)
        
        # نوار پیمایش
        scrollbar = ttk.Scrollbar(devices_frame, orient="vertical", command=self.devices_tree.yview)
        self.devices_tree.configure(yscrollcommand=scrollbar.set)
        
        # قرار دادن در فریم
        self.devices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # منوی راست کلیک
        self.create_context_menu()
    
    def create_context_menu(self):
        """ایجاد منوی راست کلیک"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="🚀 راه‌اندازی دستگاه", command=self.start_selected_device)
        self.context_menu.add_command(label="🛑 توقف دستگاه", command=self.stop_selected_device)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📦 نصب APK", command=self.install_apk_on_selected)
        self.context_menu.add_command(label="💰 اجرای Kepithor", command=self.launch_kepithor_on_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📊 نمایش اطلاعات", command=self.show_device_info)
        
        # اتصال به treeview
        self.devices_tree.bind("<Button-3>", self.show_context_menu)
    
    def create_status_bar(self, parent):
        """ایجاد نوار وضعیت"""
        self.status_bar = tk.Label(
            parent,
            text="آماده",
            bg="#333333",
            fg="#ffffff",
            font=("Arial", 10),
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def update_progress(self, value, text):
        """به‌روزرسانی نوار پیشرفت"""
        self.progress_var.set(value)
        self.progress_label.config(text=text)
        self.root.update_idletasks()
    
    def start_all_devices(self):
        """راه‌اندازی همه دستگاه‌ها"""
        self.start_all_btn.config(state="disabled", text="در حال راه‌اندازی...")
        self.status_bar.config(text="راه‌اندازی 80 دستگاه...")
        
        def start_thread():
            try:
                successful = self.farm.start_all_devices(
                    progress_callback=self.update_progress
                )
                
                self.root.after(0, lambda: self.update_button_state(
                    self.start_all_btn, "🚀 راه‌اندازی همه دستگاه‌ها (80)", "normal"
                ))
                
                self.root.after(0, lambda: self.status_bar.config(
                    text=f"✅ {successful} دستگاه راه‌اندازی شد"
                ))
                
                self.root.after(0, lambda: messagebox.showinfo(
                    "اطلاعات", f"{successful} دستگاه با موفقیت راه‌اندازی شد"
                ))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در راه‌اندازی: {e}"))
                self.root.after(0, lambda: self.update_button_state(
                    self.start_all_btn, "🚀 راه‌اندازی همه دستگاه‌ها (80)", "normal"
                ))
        
        threading.Thread(target=start_thread, daemon=True).start()
    
    def stop_all_devices(self):
        """توقف همه دستگاه‌ها"""
        if not messagebox.askyesno("تأیید", "آیا از توقف همه دستگاه‌ها اطمینان دارید؟"):
            return
        
        self.stop_all_btn.config(state="disabled", text="در حال توقف...")
        self.status_bar.config(text="توقف همه دستگاه‌ها...")
        
        def stop_thread():
            try:
                stopped = self.farm.stop_all_devices(
                    progress_callback=self.update_progress
                )
                
                self.root.after(0, lambda: self.update_button_state(
                    self.stop_all_btn, "🛑 توقف همه دستگاه‌ها", "normal"
                ))
                
                self.root.after(0, lambda: self.status_bar.config(
                    text=f"✅ {stopped} دستگاه متوقف شد"
                ))
                
                self.root.after(0, lambda: messagebox.showinfo(
                    "اطلاعات", f"{stopped} دستگاه متوقف شد"
                ))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در توقف: {e}"))
                self.root.after(0, lambda: self.update_button_state(
                    self.stop_all_btn, "🛑 توقف همه دستگاه‌ها", "normal"
                ))
        
        threading.Thread(target=stop_thread, daemon=True).start()
    
    def install_apk(self):
        """نصب APK یا XAPK"""
        apk_path = filedialog.askopenfilename(
            title="انتخاب فایل APK یا XAPK",
            filetypes=[
                ("Android packages", "*.apk;*.xapk"),
                ("APK files", "*.apk"),
                ("XAPK files", "*.xapk"),
                ("All files", "*.*")
            ]
        )
        
        if apk_path:
            file_type = "XAPK" if apk_path.lower().endswith('.xapk') else "APK"
            self.install_apk_btn.config(state="disabled", text=f"در حال نصب {file_type}...")
            self.status_bar.config(text=f"نصب {file_type} روی همه دستگاه‌ها...")

            def install_thread():
                try:
                    successful = self.farm.install_apk_on_all(
                        apk_path,
                        progress_callback=self.update_progress
                    )

                    self.root.after(0, lambda: self.update_button_state(
                        self.install_apk_btn, "📦 نصب Kepithor (APK/XAPK)", "normal"
                    ))

                    self.root.after(0, lambda: self.status_bar.config(
                        text=f"✅ {file_type} روی {successful} دستگاه نصب شد"
                    ))

                    self.root.after(0, lambda: messagebox.showinfo(
                        "اطلاعات", f"{file_type} روی {successful} دستگاه نصب شد"
                    ))

                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در نصب: {e}"))
                    self.root.after(0, lambda: self.update_button_state(
                        self.install_apk_btn, "📦 نصب Kepithor (APK/XAPK)", "normal"
                    ))
            
            threading.Thread(target=install_thread, daemon=True).start()
    
    def launch_kepithor(self):
        """اجرای Kepithor"""
        self.launch_kepithor_btn.config(state="disabled", text="در حال اجرا...")
        self.status_bar.config(text="اجرای Kepithor روی همه دستگاه‌ها...")
        
        def launch_thread():
            try:
                successful = self.farm.launch_kepithor_on_all(
                    progress_callback=self.update_progress
                )
                
                self.root.after(0, lambda: self.update_button_state(
                    self.launch_kepithor_btn, "💰 اجرای Kepithor روی همه", "normal"
                ))
                
                self.root.after(0, lambda: self.status_bar.config(
                    text=f"✅ Kepithor روی {successful} دستگاه اجرا شد"
                ))
                
                self.root.after(0, lambda: messagebox.showinfo(
                    "اطلاعات", f"Kepithor روی {successful} دستگاه اجرا شد"
                ))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در اجرا: {e}"))
                self.root.after(0, lambda: self.update_button_state(
                    self.launch_kepithor_btn, "💰 اجرای Kepithor روی همه", "normal"
                ))
        
        threading.Thread(target=launch_thread, daemon=True).start()
    
    def show_settings(self):
        """نمایش تنظیمات"""
        messagebox.showinfo("تنظیمات", "تنظیمات مزرعه (در حال توسعه)")
    
    def show_report(self):
        """نمایش گزارش"""
        stats = self.farm.get_system_stats()
        
        report = f"""
📊 گزارش عملکرد مزرعه شبیه‌ساز

📱 کل دستگاه‌ها: {stats['total_devices']}
🟢 دستگاه‌های فعال: {stats['running_devices']}
📦 APK نصب شده: {stats['installed_apps']}
💰 Kepithor فعال: {stats['active_kepithor']}

💾 استفاده از حافظه: {stats['memory_usage']:.1f}%
🔥 استفاده از CPU: {stats['cpu_usage']:.1f}%
💽 حافظه آزاد: {stats['memory_available_gb']:.1f} GB

⏰ زمان: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        messagebox.showinfo("گزارش عملکرد", report)
    
    def show_context_menu(self, event):
        """نمایش منوی راست کلیک"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def start_selected_device(self):
        """راه‌اندازی دستگاه انتخاب شده"""
        messagebox.showinfo("اطلاعات", "راه‌اندازی دستگاه انتخاب شده (در حال توسعه)")
    
    def stop_selected_device(self):
        """توقف دستگاه انتخاب شده"""
        messagebox.showinfo("اطلاعات", "توقف دستگاه انتخاب شده (در حال توسعه)")
    
    def install_apk_on_selected(self):
        """نصب APK روی دستگاه انتخاب شده"""
        messagebox.showinfo("اطلاعات", "نصب APK روی دستگاه انتخاب شده (در حال توسعه)")
    
    def launch_kepithor_on_selected(self):
        """اجرای Kepithor روی دستگاه انتخاب شده"""
        messagebox.showinfo("اطلاعات", "اجرای Kepithor روی دستگاه انتخاب شده (در حال توسعه)")
    
    def show_device_info(self):
        """نمایش اطلاعات دستگاه"""
        messagebox.showinfo("اطلاعات", "نمایش اطلاعات دستگاه (در حال توسعه)")
    
    def update_button_state(self, button, text, state):
        """به‌روزرسانی وضعیت دکمه"""
        button.config(text=text, state=state)
    
    def update_stats_timer(self):
        """به‌روزرسانی آمار به صورت دوره‌ای"""
        try:
            stats = self.farm.get_system_stats()
            
            # به‌روزرسانی برچسب‌های آمار
            self.total_devices_label.config(text=f"📱 کل دستگاه‌ها: {stats['total_devices']}")
            self.running_devices_label.config(text=f"🟢 دستگاه‌های فعال: {stats['running_devices']}")
            self.installed_apps_label.config(text=f"📦 APK نصب شده: {stats['installed_apps']}")
            self.active_kepithor_label.config(text=f"💰 Kepithor فعال: {stats['active_kepithor']}")
            self.memory_label.config(text=f"💾 حافظه: {stats['memory_usage']:.1f}%")
            self.cpu_label.config(text=f"🔥 CPU: {stats['cpu_usage']:.1f}%")
            
            # به‌روزرسانی جدول دستگاه‌ها
            self.update_devices_table()
            
        except Exception as e:
            print(f"❌ Error updating stats: {e}")
        
        # برنامه‌ریزی به‌روزرسانی بعدی
        self.root.after(3000, self.update_stats_timer)  # هر 3 ثانیه
    
    def update_devices_table(self):
        """به‌روزرسانی جدول دستگاه‌ها"""
        try:
            # پاک کردن آیتم‌های قبلی
            for item in self.devices_tree.get_children():
                self.devices_tree.delete(item)
            
            # اضافه کردن دستگاه‌ها
            devices_info = self.farm.get_devices_info()
            
            for info in devices_info[:50]:  # نمایش 50 دستگاه اول
                status = "🟢 Online" if info['is_running'] else "🔴 Offline"
                kepithor_status = "💰 Active" if info['current_app'] == 'com.KepithorStudios.KKTFaucet' else "⚫ Inactive"
                
                self.devices_tree.insert("", "end", values=(
                    info['device_id'],
                    info['ip_address'],
                    status,
                    info['specs']['name'],
                    kepithor_status,
                    f"{info['specs']['ram_mb']} MB"
                ))
                
        except Exception as e:
            print(f"❌ Error updating devices table: {e}")
    
    def run(self):
        """اجرای رابط گرافیکی"""
        self.root.mainloop()

def main():
    """تابع اصلی"""
    print("🏭 Starting Android Emulator Farm GUI...")
    
    # اجرای رابط گرافیکی
    gui = EmulatorFarmGUI()
    gui.run()

if __name__ == "__main__":
    main()
