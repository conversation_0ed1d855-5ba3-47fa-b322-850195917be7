;(function(omidGlobal) {
  var n;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}function p(a){var b='undefined'!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if('number'==typeof a.length)return{next:aa(a)};throw Error(String(a)+' is not an iterable or ArrayLike');}function q(a){if(!(a instanceof Array)){a=p(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}
var ca='function'==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},r='function'==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function da(a){a=['object'==typeof globalThis&&globalThis,a,'object'==typeof window&&window,'object'==typeof self&&self,'object'==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error('Cannot find global object');}var ea=da(this);function u(a,b){if(b)a:{var c=ea;a=a.split('.');for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&r(c,a,{configurable:!0,writable:!0,value:b})}}var fa;
if('function'==typeof Object.setPrototypeOf)fa=Object.setPrototypeOf;else{var ha;a:{var ia={a:!0},ja={};try{ja.__proto__=ia;ha=ja.a;break a}catch(a){}ha=!1}fa=ha?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+' is not extensible');return a}:null}var ka=fa;
function v(a,b){a.prototype=ca(b.prototype);a.prototype.constructor=a;if(ka)ka(a,b);else for(var c in b)if('prototype'!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.La=b.prototype}function w(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b}function x(a,b){return Object.prototype.hasOwnProperty.call(a,b)}
var la='function'==typeof Object.assign?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)x(d,e)&&(a[e]=d[e])}return a};u('Object.assign',function(a){return a||la});
u('Symbol',function(a){function b(f){if(this instanceof b)throw new TypeError('Symbol is not a constructor');return new c(d+(f||'')+'_'+e++,f)}function c(f,g){this.g=f;r(this,'description',{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=function(){return this.g};var d='jscomp_symbol_'+(1E9*Math.random()>>>0)+'_',e=0;return b});
u('Symbol.iterator',function(a){if(a)return a;a=Symbol('Symbol.iterator');for(var b='Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array'.split(' '),c=0;c<b.length;c++){var d=ea[b[c]];'function'===typeof d&&'function'!=typeof d.prototype[a]&&r(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ma(aa(this))}})}return a});function ma(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
u('WeakMap',function(a){function b(k){this.g=(h+=Math.random()+1).toString();if(k){k=p(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}}function c(){}function d(k){var l=typeof k;return'object'===l&&null!==k||'function'===l}function e(k){if(!x(k,g)){var l=new c;r(k,g,{value:l})}}function f(k){var l=Object[k];l&&(Object[k]=function(m){if(m instanceof c)return m;Object.isExtensible(m)&&e(m);return l(m)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),
m=new a([[k,2],[l,3]]);if(2!=m.get(k)||3!=m.get(l))return!1;m.delete(k);m.set(l,4);return!m.has(k)&&4==m.get(l)}catch(t){return!1}}())return a;var g='$jscomp_hidden_'+Math.random();f('freeze');f('preventExtensions');f('seal');var h=0;b.prototype.set=function(k,l){if(!d(k))throw Error('Invalid WeakMap key');e(k);if(!x(k,g))throw Error('WeakMap key fail: '+k);k[g][this.g]=l;return this};b.prototype.get=function(k){return d(k)&&x(k,g)?k[g][this.g]:void 0};b.prototype.has=function(k){return d(k)&&x(k,
g)&&x(k[g],this.g)};b.prototype.delete=function(k){return d(k)&&x(k,g)&&x(k[g],this.g)?delete k[g][this.g]:!1};return b});
u('Map',function(a){function b(){var h={};return h.H=h.next=h.head=h}function c(h,k){var l=h.g;return ma(function(){if(l){for(;l.head!=h.g;)l=l.H;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};l=null}return{done:!0,value:void 0}})}function d(h,k){var l=k&&typeof k;'object'==l||'function'==l?f.has(k)?l=f.get(k):(l=''+ ++g,f.set(k,l)):l='p_'+k;var m=h.h[l];if(m&&x(h.h,l))for(h=0;h<m.length;h++){var t=m[h];if(k!==k&&t.key!==t.key||k===t.key)return{id:l,list:m,index:h,B:t}}return{id:l,list:m,
index:-1,B:void 0}}function e(h){this.h={};this.g=b();this.size=0;if(h){h=p(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}}if(function(){if(!a||'function'!=typeof a||!a.prototype.entries||'function'!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),k=new a(p([[h,'s']]));if('s'!=k.get(h)||1!=k.size||k.get({x:4})||k.set({x:4},'t')!=k||2!=k.size)return!1;var l=k.entries(),m=l.next();if(m.done||m.value[0]!=h||'s'!=m.value[1])return!1;m=l.next();return m.done||4!=m.value[0].x||
't'!=m.value[1]||!l.next().done?!1:!0}catch(t){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,k){h=0===h?0:h;var l=d(this,h);l.list||(l.list=this.h[l.id]=[]);l.B?l.B.value=k:(l.B={next:this.g,H:this.g.H,head:this.g,key:h,value:k},l.list.push(l.B),this.g.H.next=l.B,this.g.H=l.B,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.B&&h.list?(h.list.splice(h.index,1),h.list.length||delete this.h[h.id],h.B.H.next=h.B.next,h.B.next.H=h.B.H,h.B.head=null,this.size--,
!0):!1};e.prototype.clear=function(){this.h={};this.g=this.g.H=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).B};e.prototype.get=function(h){return(h=d(this,h).B)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,function(h){return h.value})};e.prototype.forEach=function(h,k){for(var l=this.entries(),m;!(m=l.next()).done;)m=m.value,
h.call(k,m[1],m[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});u('Object.values',function(a){return a?a:function(b){var c=[],d;for(d in b)x(b,d)&&c.push(b[d]);return c}});function na(a,b){a instanceof String&&(a+='');var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}u('Array.prototype.keys',function(a){return a?a:function(){return na(this,function(b){return b})}});
u('Set',function(a){function b(c){this.g=new Map;if(c){c=p(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size}if(function(){if(!a||'function'!=typeof a||!a.prototype.entries||'function'!=typeof Object.seal)return!1;try{var c=Object.seal({x:4}),d=new a(p([c]));if(!d.has(c)||1!=d.size||d.add(c)!=d||1!=d.size||d.add({x:4})!=d||2!=d.size)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||4!=f.value[0].x||
f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;b.prototype.add=function(c){c=0===c?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;b.prototype[Symbol.iterator]=
b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b});u('Object.is',function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});u('Array.prototype.includes',function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
u('String.prototype.includes',function(a){return a?a:function(b,c){if(null==this)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(b instanceof RegExp)throw new TypeError('First argument to String.prototype.includes must not be a regular expression');return-1!==this.indexOf(b,c||0)}});u('Object.entries',function(a){return a?a:function(b){var c=[],d;for(d in b)x(b,d)&&c.push([d,b[d]]);return c}});
u('Array.prototype.values',function(a){return a?a:function(){return na(this,function(b,c){return c})}});
var y={xa:'loaded',Fa:'start',ra:'firstQuartile',Aa:'midpoint',Ga:'thirdQuartile',pa:'complete',Ba:'pause',Da:'resume',oa:'bufferStart',na:'bufferFinish',Ea:'skipped',Ja:'volumeChange',Ca:'playerStateChange',ka:'adUserInteraction'},oa={ua:'generic',Ia:'video',za:'media'},pa={ca:'native',va:'html',V:'javascript'},qa={ca:'native',V:'javascript',NONE:'none'},ra={ta:'full',qa:'domain',wa:'limited'},sa={ma:'backgrounded',sa:'foregrounded'},ta={ya:'locked',Ha:'unlocked'},ua={la:'app',Ka:'web'};function z(a,b){this.x=null!=a.x?a.x:a.left;this.y=null!=a.y?a.y:a.top;this.width=a.width;this.height=a.height;this.endX=this.x+this.width;this.endY=this.y+this.height;this.adSessionId=a.adSessionId||void 0;this.isFriendlyObstructionFor=a.isFriendlyObstructionFor||[];this.h=a.friendlyObstructionClass||void 0;this.i=a.friendlyObstructionPurpose||void 0;this.j=a.friendlyObstructionReason||void 0;this.clipsToBounds=void 0!==a.clipsToBounds?!0===a.clipsToBounds:!0;this.m=void 0!==a.hasWindowFocus?!0===
a.hasWindowFocus:!0;this.notVisibleReason=a.notVisibleReason||void 0;this.noOutputDevice=a.noOutputDevice||void 0;this.isPipActive='true'===a.isPipActive||!0===a.isPipActive||!1;this.childViews=a.childViews||[];this.isCreative=a.isCreative||!1;this.g=b}function va(a){var b={};return b.width=a.width,b.height=a.height,b}function A(a){var b={};return Object.assign({},va(a),(b.x=a.x,b.y=a.y,b))}function C(a){var b=A(a),c={};return Object.assign({},b,(c.endX=a.endX,c.endY=a.endY,c))}
function wa(a,b,c){a.x+=b;a.y+=c;a.endX+=b;a.endY+=c}z.prototype.N=function(a){if(null==a)return!1;a=A(a);var b=a.y,c=a.width,d=a.height;return this.x===a.x&&this.y===b&&this.width===c&&this.height===d};function xa(a){return a.width*a.height}function D(a){return 0===a.width||0===a.height};function ya(a,b){a=A(a);for(var c=[],d=[],e=0;e<b.length;e++){var f=A(b[e]);f=za(a,f);E(c,f.x);E(c,f.endX);E(d,f.y);E(d,f.endY)}c=c.sort(function(g,h){return g-h});d=d.sort(function(g,h){return g-h});return{ia:c,ja:d}}function za(a,b){return{x:Math.max(a.x,b.x),y:Math.max(a.y,b.y),endX:Math.min(a.x+a.width,b.x+b.width),endY:Math.min(a.y+a.height,b.y+b.height)}}function E(a,b){-1===a.indexOf(b)&&a.push(b)};function G(){this.h=this.g=this.D=this.u=this.m=this.s=void 0;this.A=0;this.l=[];this.v=[];this.C=0;this.o=[];this.j=[];this.i=[]}G.prototype.N=function(a){return null==a?!1:JSON.stringify(Aa(this))===JSON.stringify(Aa(a))};
function Aa(a){var b=[],c=[],d={viewport:a.s,adView:{percentageInView:a.A,pixelsInView:a.C,reasons:a.i},declaredFriendlyObstructions:a.l.length};if(void 0!==a.g){d.adView.geometry=A(a.g);d.adView.geometry.pixels=xa(a.g);d.adView.onScreenGeometry=A(a.h);d.adView.onScreenGeometry.pixels=Ba(a);for(var e=0;e<a.j.length;e++)b.push(A(a.j[e]));for(e=0;e<a.v.length;e++){var f=a.v[e],g=f,h={};g.h&&(h.obstructionClass=g.h);g.i&&(h.obstructionPurpose=g.i);g.j&&(h.obstructionReason=g.j);f=za(a.g,f);c.push(Object.assign({},
{x:f.x,y:f.y,width:f.endX-f.x,height:f.endY-f.y},h))}d.adView.onScreenGeometry.obstructions=b;d.adView.onScreenGeometry.friendlyObstructions=c;void 0!==a.u&&void 0!==a.D&&(d.adView.containerGeometry=A(a.u),d.adView.onScreenContainerGeometry=A(a.D),d.adView.measuringElement=!0)}return d}function Ca(a,b){b=va(b);a.s={};a.s.width=b.width;a.s.height=b.height;a.m={};a.m.x=0;a.m.y=0;a.m.width=b.width;a.m.height=b.height;a.m.endX=b.width;a.m.endY=b.height}
function Da(){return{x:0,y:0,endX:0,endY:0,width:0,height:0}}function Ea(a,b){var c={};c.x=Math.max(a.x,b.x);c.y=Math.max(a.y,b.y);c.endX=Math.min(a.endX,b.endX);c.endY=Math.min(a.endY,b.endY);c.width=Math.max(0,c.endX-c.x);c.height=Math.max(0,c.endY-c.y);return c}function Fa(a,b){return.01<b.width-a.width||.01<b.height-a.height}function Ga(a){if(-1!==a.i.indexOf('backgrounded'))a.A=0,a.C=0;else{var b=xa(a.g);if(0!==b){var c=Ba(a);a.A=Math.round(c/b*100);a.C=c}}}
function Ha(a,b){if(D(b)||!a.h)b=!1;else{var c=C(a.h),d=c.y,e=c.endX;a=c.endY;var f=b.endX;c=c.x;(f=f<c||.01>Math.abs(f-c))||(f=b.x,f=f>e||.01>Math.abs(f-e));(e=f)||(e=b.endY,e=e<d||.01>Math.abs(e-d));(d=e)||(b=b.y,d=b>a||.01>Math.abs(b-a));b=!d}return b}function H(a,b){for(var c=!1,d=0;d<a.i.length;d++)a.i[d]===b&&(c=!0);c||a.i.push(b)}
function Ba(a){var b=Math,c=b.max,d=xa(a.h),e=a.j,f=0;if(0<e.length){var g=ya(a.h,e);a=g.ia;g=g.ja;for(var h=0;h<a.length-1;h++)for(var k=(a[h]+(a[h]+1))/2,l=a[h+1]-a[h],m=0;m<g.length-1;m++){for(var t=(g[m]+(g[m]+1))/2,I=g[m+1]-g[m],ba=!1,R=0;R<e.length;R++){var F=A(e[R]);if(F.x<k&&F.x+F.width>k&&F.y<t&&F.y+F.height>t){ba=!0;break}}ba&&(f+=Math.round(l)*Math.round(I))}}return c.call(b,0,d-f)};function Ia(){};function Ja(){}
function Ka(a,b,c,d,e,f){var g=new G;b=new z(b,!1);Ca(g,b);La(a,b,g,d);if(!e)return g.i=['unmeasurable'],g.s=void 0,g.A=0,g.j=[],g.g&&(a=g.g,c={},a=new z((c.x=0,c.y=0,c.width=a.width,c.height=a.height,c),a.g),g.g=a),g.h=Da(),g;'locked'===f&&H(g,'deviceLocked');if(b.noOutputDevice)H(g,'backgrounded'),H(g,'noOutputDevice');else if('backgrounded'===c)H(g,'backgrounded');else if(void 0!==g.g){for(a=0;a<g.l.length;a++)Ha(g,g.l[a])&&g.v.push(g.l[a]);for(a=0;a<g.o.length;a++){if(c=Ha(g,g.o[a])){a:{c=g.o[a];
for(d=0;d<g.j.length;d++)if(g.j[d].N(c)){c=!0;break a}c=!1}c=!c}c&&(H(g,'obstructed'),g.j.push(g.o[a]))}Ga(g)}else H(g,'notFound');return g}
function La(a,b,c,d){var e=b.isCreative?!0:b.adSessionId===d;if(e){c.g=b;var f=C(c.g);a=Ea(c.m,f);var g=c.g;'notAttached'===g.notVisibleReason||'noWindowFocus'===g.notVisibleReason||'noAdView'===g.notVisibleReason?(H(c,'notFound'),c.h=new z(Da(),!1)):(g=c.g,'viewInvisible'===g.notVisibleReason||'viewGone'===g.notVisibleReason||'viewNotVisible'===g.notVisibleReason||'viewAlphaZero'===g.notVisibleReason||'viewHidden'===g.notVisibleReason||void 0!==c.g.notVisibleReason||D(c.g)?(H(c,'hidden'),c.h=new z(Da(),
!1)):(c.g.isPipActive&&H(c,'pictureInPicture'),c.g.m||(H(c,'backgrounded'),H(c,'noWindowFocus')),Fa(a,f)&&H(c,'clipped'),c.h=new z(a,!1)))}else if(f=!0,b.g&&(f=-1!==b.isFriendlyObstructionFor.indexOf(d)?!1:!1===b.clipsToBounds),f){g=b.childViews;for(var h=0;h<g.length;h++)f=void 0!==c.g,La(a,new z(g[h],f),c,d)}e||void 0===c.g||(b.g?-1!==b.isFriendlyObstructionFor.indexOf(d)?c.l.push(b):c.o.push(b):(e=C(b),d=C(c.h),A(c.h),!D(c.h)&&b.clipsToBounds&&(b=Ea(d,e),Fa(b,d)&&(H(c,'clipped'),c.h=new z(b,!1)))))}
;function Ma(){this.h=new Map;this.g=null}function Na(){Oa||(Oa=new Ma);return Oa}var Oa=null;function Pa(a,b){this.y=this.x=0;this.width=a;this.height=b};function Qa(){this.adSessionId=null;this.m={apiVersion:'1.0',accessMode:'limited',environment:'app',omidJsInfo:{omidImplementer:'omsdk',serviceVersion:'1.5.2-iab4528'}};this.C=null;this.A='foregrounded';this.M='unlocked';this.u=this.o='none';this.s=this.j=this.i=this.l=this.h=this.g=this.J=this.D=null;this.I=!0;this.v=new Map}
function Ra(a,b){void 0!==b.contentUrl&&(a.C=b.contentUrl,b.contentUrl=void 0);var c=a.m||{};b.omidJsInfo=Object.assign({},c.omidJsInfo||{},b.omidJsInfo||{});b=Object.assign({},c,b);a.I||(null!=a.j?(b.videoElement=a.j,b.accessMode='full'):null!=a.i&&(b.slotElement=a.i,b.accessMode='full'));a.m=b};var J=function(){if('undefined'!==typeof omidGlobal&&omidGlobal)return omidGlobal;if('undefined'!==typeof global&&global)return global;if('undefined'!==typeof window&&window)return window;if('undefined'!==typeof globalThis&&globalThis)return globalThis;var a=Function('return this')();if(a)return a;throw Error('Could not determine global object context.');}();function Sa(a,b){this.g=a;this.h=b}ea.Object.defineProperties(Sa.prototype,{event:{configurable:!0,enumerable:!0,get:function(){return this.g}},origin:{configurable:!0,enumerable:!0,get:function(){return this.h}}});function K(){var a=w.apply(0,arguments);Ta(function(){throw new (Function.prototype.bind.apply(Error,[null,'Could not complete the test successfully - '].concat(q(a))));},function(){return console.error.apply(console,q(a))})}function Ua(){var a=w.apply(0,arguments);Ta(function(){},function(){return console.error.apply(console,q(a))})}function Ta(a,b){'undefined'!==typeof jasmine&&jasmine?a():'undefined'!==typeof console&&console&&console.error&&b()};function Va(a){this.g=a;this.m=[];this.i=[];this.j=[];this.l=[];this.o={}}function Wa(a,b){if(void 0!==a.g&&a.g.adSessionId&&!1!==Xa(a,b)){var c=b.event;a.j.filter(function(d){return d.type===c.type}).forEach(function(d){a.h(d.G,c)})}}function Ya(a,b){a.m.push(b);Wa(a,b)}function Za(a,b,c){void 0!==a.g&&a.g.adSessionId&&a.m.filter(function(d){return d.event.type===b&&Xa(a,d)}).map(function(d){return d.event}).forEach(function(d){a.h(c.G,d)})}
function Xa(a,b){var c=b.event.type,d=-1!==Object.values(y).indexOf(c)&&'volumeChange'!==c;return'impression'===c||'loaded'===c&&a.g.h?b.origin===a.g.u:d?b.origin===a.g.o:!0}function $a(a,b,c){Object.keys(y).forEach(function(d){d=y[d];var e={type:d,R:c,G:b};a.j.push(e);Za(a,d,e)})}function ab(a,b,c,d){var e={U:c,R:d,G:b};a.l.push(e);a.i.forEach(function(f){var g=bb(f);'sessionStart'===f.event.type&&cb(a,g,e);a.h(e.G,g)})}
function db(a,b,c){var d=L(a,'sessionError','native',{errorType:b,message:c});a.i.push(d);a.l.forEach(function(e){a.h(e.G,d.event)})}function eb(a,b){a.o=Object.assign(a.o,b);b=a.g.m;if(void 0!==b){b=Object.assign({},fb(gb(a,hb(a,{context:b}),!0)),{supportsLoadedEvent:!!a.g.h||'video'==a.g.g});Object.assign(b,{pageUrl:null,contentUrl:a.g.C});var c=L(a,'sessionStart','native',b);a.i.push(c);a.l.forEach(function(d){var e=bb(c);cb(a,e,d);a.h(d.G,e)},a);ib(a)}}
function cb(a,b,c){c.U&&(b.data.verificationParameters=a.o[c.U]);c.R&&(c=a.g.v.get(c.R))&&(b.data.verificationParameters=c.verificationParameters,b.data.context.accessMode=c.accessMode,'full'===c.accessMode&&(a.g.j&&(b.data.context.videoElement=a.g.j),a.g.i&&(b.data.context.slotElement=a.g.i)))}function jb(a){var b=L(a,'sessionFinish','native');a.i.push(b);a.l.forEach(function(c){a.h(c.G,b.event)})}Va.prototype.h=function(a){var b=w.apply(1,arguments);try{a.apply(null,q(b))}catch(c){Ua(c)}};
function kb(a,b){var c=(c=a.g.K)?Aa(c):null;c=gb(a,hb(a,c));Ya(a,L(a,'impression',b,c))}function lb(a,b,c){if(a.g.h||'display'!=a.g.g)b=L(a,'loaded',b,gb(a,hb(a,void 0===c?null:c))),Ya(a,b)}
function mb(a,b,c,d){'start'!==b&&'volumeChange'!==b||null!=(d&&d.deviceVolume)||(d.deviceVolume=a.g.D);if(d&&('start'===b||'volumeChange'===b)){var e=d.videoPlayerVolume,f=d.mediaPlayerVolume;null!=e?(Object.assign(d,{mediaPlayerVolume:e}),a.g.J=e):null!=f&&(Object.assign(d,{videoPlayerVolume:f}),a.g.J=f)}Ya(a,L(a,b,c,d))}
function ib(a){var b=a.m.filter(function(f){return Object.values(y).includes(f.event.type)&&'video'==a.g.g&&f.origin===a.g.o||'loaded'==f.event.type&&'display'==a.g.g&&f.origin===a.g.u?!0:!1}).map(function(f){return f.event}),c=a.g.adSessionId||'',d={};b=p(b);for(var e=b.next();!e.done;d={F:d.F},e=b.next()){d.F=e.value;d.F.adSessionId||(d.F.adSessionId=c);if('loaded'==d.F.type){if(!a.g.h&&'display'==a.g.g)continue;d.F.data=gb(a,hb(a,d.F.data))}a.j.filter(function(f){return function(g){return g.type===
f.F.type}}(d)).forEach(function(f){return function(g){return g.G(f.F)}}(d))}}function nb(a,b,c){a:{c=new Set(c);a=p(a.m.concat(a.i));for(var d=a.next();!d.done;d=a.next())if(d=d.value,c.has(d.event.type)&&d.origin!=b){b=!0;break a}b=!1}return b?(K('Event owner cannot be registered after its events have already been published.'),!1):!0}function ob(a,b){nb(a,b,Object.values(y))&&pb(a,b)&&(a.g.o=b)}function qb(a,b){nb(a,b,['impression'])&&rb(a,b)&&(a.g.u=b)}
function rb(a,b){var c=a.g.u;return'none'!=c&&c!=b?(K('Impression event is owned by '+(a.g.u+', not ')+(b+'.')),!1):!0}function pb(a,b){var c=a.g.o;return'none'!=c&&c!=b?(K('Media events are owned by '+(a.g.o+', not '+b+'.')),!1):!0}function gb(a,b,c){c=void 0===c?!1:c;b=Object.assign({},b);a.g.g&&Object.assign(b,{mediaType:a.g.g});a.g.h&&(c||'definedByJavaScript'!==a.g.h)&&Object.assign(b,{creativeType:a.g.h});return b}function fb(a){var b=Na().g;return b?Object.assign({},a,{lastActivity:b}):a}
function hb(a,b){return a.g.l?Object.assign({},b,{impressionType:a.g.l}):b}function L(a,b,c,d){return new Sa({adSessionId:a.g.adSessionId||'',timestamp:(new Date).getTime(),type:b,data:d},c)}function bb(a){a=a.event;var b=a.data?Object.assign({},a.data):void 0;'sessionStart'===a.type&&(b.context=Object.assign({},b.context));return{adSessionId:a.adSessionId,timestamp:a.timestamp,type:a.type,data:b}};function sb(a,b,c){this.h=a;this.i=b;this.g=c}
function M(a,b,c){'container'===b&&void 0!==a.g.L&&void 0!==a.g&&null!=a.g.adSessionId&&(a.g.O=Ka(a.i,a.g.L,a.g.A,a.g.adSessionId,!0,a.g.M));b=a.g;var d=b.O,e=b.P;if(d)if(e){b=new G;var f=d.s,g=d.g,h=d.h,k=e.g,l=e.h;f&&g&&h&&k&&l&&(Ca(b,f),b.u=new z(g,!1),b.D=new z(h,!1),b.o=Object.assign([],d.o),b.j=Object.assign([],d.j),b.l=Object.assign([],d.l),b.v=Object.assign([],d.v),b.i=Object.assign([],e.i,d.i),d=b.u.x,e=b.u.y,k=new z(k,!1),l=new z(l,!1),wa(k,d,e),wa(l,d,e),b.g=k,b.h=Ea(l,h),Ga(b))}else b=
d;else b=null;h=a.g.K;if(b&&!b.N(h)||c)h=Aa(b),c&&(h.adView.reasons=h.adView.reasons||[c]),c=a.h,'audio'!=c.g.h&&Ya(c,L(c,'geometryChange','native',h)),a.g.K=b};function tb(){var a;this.g=a=void 0===a?omidGlobal:a}tb.prototype.setInterval=function(a,b){return ub(this,'setInterval')(a,b)};tb.prototype.clearInterval=function(a){ub(this,'clearInterval')(a)};function vb(a,b){ub(a,'clearTimeout')(b)}function ub(a,b){return a.g&&a.g[b]?a.g[b]:wb(a,b)}
function xb(a,b,c,d){if(a.g.document&&a.g.document.body){var e=a.g.document.createElement('img');e.width=1;e.height=1;e.style.display='none';e.src=b;c&&e.addEventListener('load',function(){return c()});d&&e.addEventListener('error',function(){return d()});a.g.document.body.appendChild(e)}else wb(a,'sendUrl')(b,c,d)}function wb(a,b){if(a.g&&a.g.omidNative&&a.g.omidNative[b])return a.g.omidNative[b].bind(a.g.omidNative);throw Error('Native interface method "'+b+'" not found.');};function N(a){return'object'===typeof a}function O(a){return'number'===typeof a&&!isNaN(a)&&0<=a}function P(a){return'string'===typeof a}function Q(a,b){return P(a)&&-1!==Object.values(b).indexOf(a)}function yb(a){return!(!a||!a.tagName||'iframe'!==a.tagName.toLowerCase())};function S(a,b,c,d,e){this.j=a;this.ba=b;this.L=c;this.i=d;this.P=e;this.h=null;this.g=this.m=this.D=void 0;this.O=!0;this.J=void 0;zb(this)}
function zb(a){if(!a.h){var b;a:{if((b=a.j.document)&&b.getElementsByClassName&&(b=b.getElementsByClassName('omid-element'))){if(1==b.length){b=b[0];break a}1<b.length&&a.O&&(db(a.L,'generic',"More than one element with 'omid-element' class name."),a.O=!1)}b=null}if(b&&b.tagName&&'video'===b.tagName.toLowerCase())a.i.j=b;else if(b&&b.tagName)a.i.i=b;else return;Ab(a)}}function Ab(a){a.i.j?(a.h=a.i.j,a.o()):a.i.i&&(a.h=a.i.i,yb(a.h)?a.i.s&&a.o():a.o())}
function Bb(a){a.g&&(yb(a.h)?a.i.s&&(a.K(),Cb(a)):(a.K(),Cb(a)))}S.prototype.A=function(){this.J&&(this.j.document.removeEventListener('visibilitychange',this.J),this.J=void 0)};S.prototype.o=function(){};function Cb(a){a.D&&(a.i.P=a.D,M(a.ba,'creative'))}function Db(a){if(a.g&&a.i.s){var b=new z(a.i.s,!1);wa(b,a.g.x,a.g.y);b.clipsToBounds=!0;return b}};function T(a,b,c,d,e,f){S.call(this,a,c,d,e,f);this.s=b;this.l=void 0}v(T,S);T.prototype.A=function(){void 0!==this.l&&(this.P.clearInterval(this.l),this.l=void 0);S.prototype.A.call(this)};T.prototype.o=function(){var a=this;S.prototype.o.call(this);null==this.h?this.l=void 0:void 0===this.l&&(this.l=this.P.setInterval(function(){return Eb(a)},200),Eb(this))};
T.prototype.K=function(){if(this.m){var a=Db(this);if(a){this.g.isCreative=!1;a.isCreative=!0;for(var b=!1,c=0;c<this.g.childViews.length;c++)if(this.g.childViews[c].isCreative){this.g.childViews[c]=a;b=!0;break}b||this.g.childViews.push(a)}else this.g.isCreative=!0;this.D=Ka(this.s,this.m,this.i.A,this.i.adSessionId,this.I())}};T.prototype.I=function(){return!0};
function Eb(a){if(void 0!==a.l){b:{try{var b=a.j.top;var c=0<=b.innerHeight&&0<=b.innerWidth;break b}catch(d){}c=!1}c?(c=a.j.top,c=new z(new Pa(c.innerWidth,c.innerHeight),!1)):c=new z(new Pa(0,0),!1);b=a.h.getBoundingClientRect();if(null==b.x||isNaN(b.x))b.x=b.left;if(null==b.y||isNaN(b.y))b.y=b.top;b=new z(b,!1);c.N(a.m)&&b.N(a.g)||(a.g=b,a.g.clipsToBounds=!0,a.m=c,a.m.childViews.push(a.g),Bb(a))}};function U(a,b,c,d,e,f){S.call(this,a,c,d,e,f);this.v=this.s=this.u=this.l=void 0;this.M=!1;this.C=void 0}v(U,S);U.prototype.A=function(){this.l&&this.l.disconnect();Fb(this);S.prototype.A.call(this)};U.prototype.o=function(){S.prototype.o.call(this);if(this.h&&(this.l||(this.l=Gb(this)),Hb(this),Ib(this.h)&&Jb(this),'backgrounded'===this.i.A)){var a=Kb(new Pa(0,0));this.C=this.g=this.m=a;Bb(this)}};
U.prototype.K=function(){if(this.g&&this.C){var a=Db(this);if(a){var b=a;var c=this.C;var d=Math.max(a.x,c.x);var e=Math.max(a.y,c.y),f=Math.min(a.endX,c.endX);a=Math.min(a.endY,c.endY);f<=d||a<=e?d=null:(c={},d=new z((c.x=d,c.y=e,c.width=Math.abs(f-d),c.height=Math.abs(a-e),c),!1));d||(d=new z({x:0,y:0,width:0,height:0},!1))}else b=this.g,d=this.C;e=new G;this.m&&Ca(e,this.m);e.g=b;e.h=d;Ga(e);this.M?D(e.g)?H(e,'hidden'):100===e.A||H(e,'clipped'):H(e,'viewport');this.D=e}};U.prototype.I=function(){return!0};
function Fb(a){a.u&&(a.u.disconnect(),a.u=void 0);a.s&&(a.s.disconnect(),a.s=void 0);a.v&&((0,a.j.removeEventListener)('resize',a.v),a.v=void 0)}function Hb(a){a.l&&a.h&&(a.l.unobserve(a.h),a.l.observe(a.h))}function Ib(a){a=a.getBoundingClientRect();return 0==a.width||0==a.height}
function Gb(a){return new a.j.IntersectionObserver(function(b){try{if(b.length){for(var c,d=b[0],e=1;e<b.length;e++)b[e].time>d.time&&(d=b[e]);c=d;a.m=Kb(c.rootBounds);a.g=Kb(c.boundingClientRect);a.C=Kb(c.intersectionRect);a.M=!!c.isIntersecting;Bb(a)}}catch(f){a.A(),db(a.L,'generic','Problem handling IntersectionObserver callback: '+f.message)}},{root:null,rootMargin:'0px',threshold:[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1]})}
function Jb(a){a.j.ResizeObserver?a.u||(a.u=Lb(a,function(){return Mb(a)}),a.u.observe(a.h)):(a.v||(a.v=function(){return Mb(a)},(0,a.j.addEventListener)('resize',a.v)),a.s||(a.s=new MutationObserver(function(){return Mb(a)}),a.s.observe(a.h,{childList:!1,attributes:!0,subtree:!1})))}function Mb(a){a.h&&!Ib(a.h)&&(Hb(a),Fb(a))}function Lb(a,b){return new a.j.ResizeObserver(b)}function Kb(a){if(a&&null!==a.x&&null!==a.y&&null!==a.width&&null!==a.height)return new z(a,!1)};function Nb(a){if('object'===typeof a&&'object'===typeof a.webOSSystem)return a.webOSSystem}function Ob(a){if('object'===typeof a&&'object'===typeof a.tizen)return a.tizen}function Pb(a){return'object'===typeof Ob(a)};function Qb(a,b){this.h=a;this.g=b};function Sb(){return'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g,function(a){var b=16*Math.random()|0;return'y'===a?(b&3|8).toString(16):b.toString(16)})};function Tb(a,b){var c=void 0===c?J:c;this.j=a;this.g=c;this.i=b;this.h=[]}
function Ub(a){if(!a.g||!a.g.document)throw Error('OMID Service Script is not running within a window.');var b=a.h;a.h=[];b.forEach(function(c){try{var d=a.i.I?'limited':'full',e=Q(c.accessMode,ra)?c.accessMode:null;var f=e?'full'==e&&'limited'==d?d:'domain'==e?'limited':e:d;c.accessMode=f;a:{var g=c.resourceUrl,h=a.g.location.origin;try{var k=new URL(g,h);break a}catch(I){}try{k=new URL(g);break a}catch(I){}k=null}if(d=k){var l=Sb();Vb(a,l,d,f);var m=c.vendorKey,t=c.verificationParameters;m=void 0===
m?'':m;t=void 0===t?'':t;m&&'string'===typeof m&&''!==m&&t&&'string'===typeof t&&''!==t&&(a.j.o[m]=t);a.i.v.set(l,c)}}catch(I){Ua('OMID verification script '+c.resourceUrl+' failed to load: '+I)}})}
function Vb(a,b,c,d){var e=a.g.document,f=e.createElement('iframe');f.id='omid-verification-script-frame-'+b;f.style.display='none';if('full'==d){var g=function(){var h=f.contentWindow;h.omidVerificationProperties={serviceWindow:a.g,injectionSource:'app',injectionId:b};h=h.document;var k=h.createElement('script');k.src=c.href;h.head.appendChild(k);f.removeEventListener('load',g)};f.addEventListener('load',g)}else'limited'==d?f.srcdoc="<html><head>\n<script type=\"text/javascript\">window['omidVerificationProperties'] = {\n'serviceWindow': window.parent,\n'injectionSource': 'app',\n'injectionId': '"+
(b+'\',\n};\x3c/script>\n<script type="text/javascript" src="')+c.href+'">\x3c/script>\n</head><body></body></html>':'domain'==d&&(f.src=Wb(a,b,c).href);['domain','limited'].includes(d)&&(f.sandbox='allow-scripts');e.body.appendChild(f);Na().h.set(b,f)}
function Wb(a,b,c){var d='/.well-known/omid/omloader-v1.html#';(new Map([['verificationScriptUrl',c.href],['injectionId',b]])).forEach(function(e,f){d+=encodeURIComponent(f)+'='+encodeURIComponent(e)+'&'});b=null;try{b=new URL(d,a.g.parent.location.origin)}catch(e){throw Error('OMID Service Script cannot access the parent window.');}return b};function Xb(a,b,c,d,e,f){var g=this;this.g=a;this.h=b;this.j=c;this.i=d;this.l=e;this.s=f;this.m=!1;Yb(this,function(h){if('sessionStart'===h.type){g.m=!0;try{Ub(g.l)}catch(k){K(k.message)}}'sessionFinish'===h.type&&(g.m=!1)})}function Yb(a,b,c,d){ab(a.h,b,c,d)}n=Xb.prototype;n.setSlotElement=function(a){a&&a.tagName?(this.g.i=a,this.i&&Ab(this.i)):K('setSlotElement called with a non-HTMLElement.  It will be ignored.')};n.setElementBounds=function(a){this.g.s=a;this.i&&Ab(this.i);this.i&&Bb(this.i)};
n.error=function(a,b){db(this.h,a,b)};n.injectVerificationScriptResources=function(a){var b=this.l;b.h.push.apply(b.h,q(a));if(this.m)try{Ub(this.l)}catch(c){K(c.message)}};n.setCreativeType=function(a,b){b=void 0===b?null:b;if(!this.g.g||this.g.h)this.g.h=a,'video'==a||'audio'==a?this.g.g='video':'htmlDisplay'==a||'nativeDisplay'==a?this.g.g='display':'definedByJavaScript'==a&&b&&(this.g.g='none'==b?'display':'video')};n.setImpressionType=function(a){if(!this.g.g||this.g.h)this.g.l=a};
n.setClientInfo=function(a,b,c){var d=this.g.m||{};d.omidJsInfo=Object.assign({},d.omidJsInfo,{sessionClientVersion:a,partnerName:b,partnerVersion:c});this.g.m=d;return this.g.m.omidJsInfo.serviceVersion};function Zb(a,b){if(!b)return a.g;for(var c=p(a.h.values()),d=c.next();!d.done;d=c.next())if(d=d.value,d.g.v.has(b))return d;return a.g}function V(a,b){return null==b?a.g:a.h.get(b)||a.g}function $b(a){var b=new Qa,c=new Va(b),d=new Ia,e=new Ja,f=new sb(c,e,b);a=a.i;var g=omidGlobal;d=g?g.IntersectionObserver&&(g.MutationObserver||g.ResizeObserver)?new U(g,d,f,c,b,a):new T(g,e,f,c,b,a):null;return new Xb(b,c,f,d,new Tb(c,b),new Qb(c,b))};function ac(a){return a&&N(a)?Object.entries(a).reduce(function(b,c){var d=p(c);c=d.next().value;d=d.next().value;return b&&P(c)&&null!=d&&N(d)&&P(d.resourceUrl)},!0):!1};function W(a,b,c,d){this.h=a;this.method=b;this.version=c;this.g=d}function bc(a){return!!a&&void 0!==a.omid_message_guid&&void 0!==a.omid_message_method&&void 0!==a.omid_message_version&&'string'===typeof a.omid_message_guid&&'string'===typeof a.omid_message_method&&'string'===typeof a.omid_message_version&&(void 0===a.omid_message_args||void 0!==a.omid_message_args)}function cc(a){return new W(a.omid_message_guid,a.omid_message_method,a.omid_message_version,a.omid_message_args)}
function dc(a){var b={};b=(b.omid_message_guid=a.h,b.omid_message_method=a.method,b.omid_message_version=a.version,b);void 0!==a.g&&(b.omid_message_args=a.g);return b};function ec(a){this.i=a};function X(a){this.i=a;this.handleExportedMessage=X.prototype.j.bind(this)}v(X,ec);X.prototype.h=function(a,b){b=void 0===b?this.i:b;if(!b)throw Error('Message destination must be defined at construction time or when sending the message.');b.handleExportedMessage(dc(a),this)};X.prototype.j=function(a,b){bc(a)&&this.g&&this.g(cc(a),b)};function fc(a){return gc(a,'SessionService.')}function gc(a,b){return(a=a.match(new RegExp('^'+b+'(.*)')))&&a[1]};function hc(a,b){this.i=b=void 0===b?J:b;var c=this;a.addEventListener('message',function(d){if('object'===typeof d.data){var e=d.data;bc(e)&&d.source&&c.g&&c.g(cc(e),d.source)}})}v(hc,ec);hc.prototype.h=function(a,b){b=void 0===b?this.i:b;if(!b)throw Error('Message destination must be defined at construction time or when sending the message.');b.postMessage(dc(a),'*')};function ic(a,b){b=void 0===b?{}:b;var c=J.webkit?J.webkit.messageHandlers.omidJsSessionService:J.omidJsSessionService;if(!c)return!1;c.postMessage(JSON.stringify({method:a,data:b}));return!0};function jc(){this.g=kc}n=jc.prototype;n.registerSessionObserver=function(a,b){Yb(V(this.g,a),b)};n.setSlotElement=function(a,b){V(this.g,a).setSlotElement(b)};n.setElementBounds=function(a,b){V(this.g,a).setElementBounds(b)};
function lc(a,b,c,d,e){e=void 0===e?null:e;a=V(a.g,b);var f=a.g.m;f.sessionOwner||(f.sessionOwner=d?'native':'javascript');d?(b=b||Sb(),a.g.adSessionId=b,c.canMeasureVisibility=a.i.I(),Ra(a.g,c),eb(a.h,e),a.i&&zb(a.i)):b?(Ra(a.g,c),ic('startSession',{adSessionId:b})||K('On App, the native-layer JS Session Service must be initialized before starting an ad session using the JS API.')):K('Session client must be updated to start an App session from JS.')}
function mc(a,b,c){c?(b=V(a.g,b),a=a.g,a.g=$b(a),jb(b.h),b.i.A(),b.o&&(b.o.stop(),b.o=null)):b?ic('finishSession',{adSessionId:b})||K('On App, the native-layer JS Session Service must be initialized before finishing an ad session using the JS API.'):K('Session client must be updated to finish an App session from JS.')}n.error=function(a,b,c){V(this.g,a).error(b,c)};
function Y(a,b,c,d){a=V(a.g,b);'impression'==c?rb(a.h,'javascript')&&(kb(a.h,'javascript'),a.i&&zb(a.i)):('loaded'==c?(d=void 0===d?null:d,pb(a.h,'javascript')&&lb(a.h,'javascript',d)):pb(a.h,'javascript')&&mb(a.h,c,'javascript',d),['loaded','start'].includes(c)&&a.i&&zb(a.i))}n.injectVerificationScriptResources=function(a,b){V(this.g,a).injectVerificationScriptResources(b)};n.setCreativeType=function(a,b,c){c=void 0===c?null:c;V(this.g,a).setCreativeType(b,c)};
n.setImpressionType=function(a,b){V(this.g,a).setImpressionType(b)};n.setClientInfo=function(a,b,c,d){var e=this.g;if(null==a)a=e.g;else{var f=[].concat(q(e.h.values())).includes(e.g)?$b(e):e.g;e.h.set(a,f);a=f}return a.setClientInfo(b,c,d)};function nc(a){a=a.split('-')[0].split('.');for(var b=['1','0','3'],c=0;3>c;c++){var d=parseInt(a[c],10),e=parseInt(b[c],10);if(d>e)break;else if(d<e)return!1}return!0};function oc(a,b){return/\d+\.\d+\.\d+(-.*)?/.test(a)&&nc(a)?b:JSON.stringify(b)}function pc(a,b){return/\d+\.\d+\.\d+(-.*)?/.test(a)&&nc(a)?b?b:[]:b&&'string'===typeof b?JSON.parse(b):[]};function qc(){var a=rc;var b=void 0===b?omidGlobal:b;this.g=a;this.h=b;this.j=new X;this.h.omid=this.h.omid||{};this.h.omid.v1_SessionServiceCommunication=this.j;this.i=b&&b.addEventListener&&b.postMessage?new hc(b):null;this.j.g=this.m.bind(this);this.i&&(this.i.g=this.l.bind(this))}qc.prototype.m=function(a,b){null!=fc(a.method)&&sc(this,a,b,this.j)};qc.prototype.l=function(a,b){null!=fc(a.method)&&sc(this,a,b,this.i)};
function sc(a,b,c,d){function e(){var k=new W(f,'response',h,oc(h,w.apply(0,arguments)));d.h(k,c)}var f=b.h,g=b.method,h=b.version;b=pc(h,b.g);try{tc(a,g,e,b)}catch(k){d.h(new W(f,'error',h,'\n        name: '+k.name+'\n        message: '+k.message+'\n        filename: '+k.filename+'\n        lineNumber: '+k.lineNumber+'\n        columnNumber: '+k.columnNumber+'\n        stack: '+k.stack+'\n        toString(): '+k.toString()),c)}}
function tc(a,b,c,d){if(null!=fc(b))switch(fc(b)){case 'registerAdEvents':c=p(d).next().value;qb(V(a.g.g,c).h,'javascript');break;case 'registerMediaEvents':c=p(d).next().value;ob(V(a.g.g,c).h,'javascript');break;case 'registerSessionObserver':var e=p(d).next().value;a.g.registerSessionObserver(e,c);break;case 'setSlotElement':e=p(d);c=e.next().value;e=e.next().value;a.g.setSlotElement(e,c);break;case 'setVideoElement':e=p(d);c=e.next().value;e=e.next().value;a=V(a.g.g,e);c&&c.tagName&&'video'===
c.tagName.toLowerCase()?(a.g.j=c,a.i&&Ab(a.i)):K('setVideoElement called with a non-HTMLVideoElement. It will be ignored.');break;case 'setElementBounds':e=p(d);c=e.next().value;e=e.next().value;a.g.setElementBounds(e,c);break;case 'startSession':c=p(d);d=c.next().value;c=c.next().value;b=a.h;if(null!=d&&N(d)){var f=d.customReferenceData;d=d.underEvaluation;P(f)||(f=void 0);'boolean'===typeof d||(d=!1);d={customReferenceData:f,underEvaluation:d};if('object'===typeof Nb(b)){var g=Nb(b),h;'object'===
typeof g&&(h=g.identifier);d.app={appId:h&&'string'===typeof h?h:void 0};b:if(g=Nb(b),'object'===typeof g){try{e=JSON.parse(g.deviceInfo)}catch(m){e=void 0;break b}e={deviceType:e.modelName||'Unknown',osVersion:e.platformVersion||'Unknown',os:'webOS'}}else e=void 0;d.deviceInfo=e;d.deviceCategory='ctv'}else if(Pb(b)){b:{if(Pb(b)){try{var k=Ob(b).application.getCurrentApplication().appInfo.id}catch(m){e=void 0;break b}if('string'===typeof k){e=k;break b}}e=void 0}d.app={appId:e};if(Pb(b)){e=Ob(b).systeminfo;
if('object'===typeof e&&'function'===typeof e.getCapability){g=e.getCapability('http://tizen.org/system/model_name');var l=e.getCapability('http://tizen.org/feature/platform.version')}e={deviceType:g||'Unknown',osVersion:l||'Unknown',os:'tizen'}}else e=void 0;d.deviceInfo=e;Pb(b)&&'object'===typeof Ob(b).tvinputdevice&&(d.deviceCategory='ctv')}}else d=null;if(null==d)break;lc(a.g,c,d,!1);break;case 'finishSession':c=p(d).next().value;mc(a.g,c,!1);break;case 'impressionOccurred':c=p(d).next().value;
Y(a.g,c,'impression');break;case 'loaded':e=p(d);c=e.next().value;e=e.next().value;c?(g={skippable:c.isSkippable,autoPlay:c.isAutoPlay,position:c.position},c.isSkippable&&(g.skipOffset=c.skipOffset),Y(a.g,e,'loaded',g)):Y(a.g,e,'loaded');break;case 'start':g=p(d);c=g.next().value;e=g.next().value;g=g.next().value;Y(a.g,g,'start',{duration:c,mediaPlayerVolume:e});break;case 'firstQuartile':c=p(d).next().value;Y(a.g,c,'firstQuartile');break;case 'midpoint':c=p(d).next().value;Y(a.g,c,'midpoint');break;
case 'thirdQuartile':c=p(d).next().value;Y(a.g,c,'thirdQuartile');break;case 'complete':c=p(d).next().value;Y(a.g,c,'complete');break;case 'pause':c=p(d).next().value;Y(a.g,c,'pause');break;case 'resume':c=p(d).next().value;Y(a.g,c,'resume');break;case 'bufferStart':c=p(d).next().value;Y(a.g,c,'bufferStart');break;case 'bufferFinish':c=p(d).next().value;Y(a.g,c,'bufferFinish');break;case 'skipped':c=p(d).next().value;Y(a.g,c,'skipped');break;case 'volumeChange':e=p(d);c=e.next().value;e=e.next().value;
Y(a.g,e,'volumeChange',{mediaPlayerVolume:c});break;case 'playerStateChange':e=p(d);c=e.next().value;e=e.next().value;Y(a.g,e,'playerStateChange',{state:c});break;case 'adUserInteraction':e=p(d);c=e.next().value;e=e.next().value;Y(a.g,e,'adUserInteraction',{interactionType:c});break;case 'setClientInfo':h=p(d);e=h.next().value;g=h.next().value;l=h.next().value;h=h.next().value;a=a.g.setClientInfo(h,e,g,l);c(a);break;case 'injectVerificationScriptResources':e=p(d);c=e.next().value;e=e.next().value;
a.g.injectVerificationScriptResources(e,c);break;case 'setCreativeType':e=p(d);c=e.next().value;e=e.next().value;a.g.setCreativeType(e,c);break;case 'setImpressionType':e=p(d);c=e.next().value;e=e.next().value;a.g.setImpressionType(e,c);break;case 'setContentUrl':e=p(d);c=e.next().value;e=e.next().value;V(a.g.g,e).g.C=c;break;case 'sessionError':g=p(d),c=g.next().value,e=g.next().value,g=g.next().value,a.g.error(g,c,e)}};function Z(){this.g=rc}n=Z.prototype;
n.da=function(a,b){if(!(!(a&&N(a)&&Q(a.impressionOwner,qa))||'videoEventsOwner'in a&&null!=a.videoEventsOwner&&!Q(a.videoEventsOwner,qa)||'mediaEventsOwner'in a&&null!=a.mediaEventsOwner&&!Q(a.mediaEventsOwner,qa))){b=V(this.g.g,b);if(a.creativeType&&a.impressionType){var c=a.mediaEventsOwner;null==b.g.h&&b.setCreativeType(a.creativeType,c);null==b.g.l&&(b.g.l=a.impressionType);ob(b.h,c)}else c=a.videoEventsOwner,b.g.g=null==c||'none'===c?'display':'video',b.g.h=null,b.g.l=null,ob(b.h,c);qb(b.h,a.impressionOwner);
a&&null!=a.isolateVerificationScripts&&'boolean'===typeof a.isolateVerificationScripts&&(b.g.I=a.isolateVerificationScripts)}};
n.aa=function(a,b,c,d){b&&'string'===typeof b.adSessionType&&(b.adSessionType=b.adSessionType.toLowerCase());var e;if(N(b)){if(e=Q(b.environment,ua)&&Q(b.adSessionType,pa))e=b.omidNativeInfo,e=N(e)?P(e.partnerName)&&P(e.partnerVersion):!1;e&&(e=b.app,e=N(e)?P(e.libraryVersion)&&P(e.appId):!1)}else e=!1;if(e){if(ac(d)){e=p(Object.values(d));for(var f=e.next();!f.done;f=e.next())f.value.accessMode='limited';V(this.g.g,a).g.v=new Map(Object.entries(d))}lc(this.g,a,b,!0,c)}else K('Native ad session context invalid; session not started.')};
n.W=function(a){mc(this.g,a,!0)};n.$=function(a,b){N(a)&&O(a.x)&&O(a.y)&&O(a.width)&&O(a.height)&&(b=V(this.g.g,b),b.g.L=a,M(b.j,'container'))};n.ha=function(a,b){Q(a,sa)&&(b=V(this.g.g,b),b.g.A=a,'backgrounded'===a?M(b.j,'container','backgrounded'):M(b.j,'container'))};n.Y=function(a,b){Q(a,ta)&&(b=V(this.g.g,b),b.g.M=a,'locked'===a?M(b.j,'container','deviceLocked'):M(b.j,'container'))};n.ea=function(a){'impression'===a&&this.T()};n.T=function(a){a=V(this.g.g,a);rb(a.h,'native')&&kb(a.h,'native')};
n.X=function(a,b){this.S('loaded',void 0===a?null:a,b)};n.error=function(a,b,c){Q(a,oa)&&this.g.error(c,a,b)};n.fa=function(a,b,c){this.S(a,b,c)};n.S=function(a,b,c){Q(a,y)&&(void 0===b||N(b))&&(c=V(this.g.g,c),pb(c.h,'native')&&('loaded'==a?lb(c.h,'native',b):mb(c.h,a,'native',b)))};n.Z=function(a,b){b=V(this.g.g,b);'none'===b.h.g.o||'number'!==typeof a||isNaN(a)||(b.g.D=a,a=b.s,b=a.g.J,null!=b&&mb(a.h,'volumeChange','native',{mediaPlayerVolume:b,deviceVolume:a.g.D}))};
n.ga=function(a){if(a&&N(a)&&O(a.timestamp)){var b=Na(),c=(b.g||{}).timestamp;if(!c||c<a.timestamp)b.g=a}};Z.prototype.startSession=Z.prototype.aa;Z.prototype.error=Z.prototype.error;Z.prototype.finishSession=Z.prototype.W;Z.prototype.publishAdEvent=Z.prototype.ea;Z.prototype.publishImpressionEvent=Z.prototype.T;Z.prototype.publishVideoEvent=Z.prototype.fa;Z.prototype.publishMediaEvent=Z.prototype.S;Z.prototype.publishLoadedEvent=Z.prototype.X;Z.prototype.setNativeViewHierarchy=Z.prototype.$;
Z.prototype.setState=Z.prototype.ha;Z.prototype.setDeviceLockState=Z.prototype.Y;Z.prototype.setDeviceVolume=Z.prototype.Z;Z.prototype.init=Z.prototype.da;Z.prototype.setLastActivity=Z.prototype.ga;function uc(){var a=kc,b=vc,c=this;var d=void 0===d?J:d;this.j=a;this.g=b;this.l={};this.m={};this.i=new X;d.omid=d.omid||{};d.omid.v1_VerificationServiceCommunication=this.i;this.h=null;d&&d.addEventListener&&d.postMessage&&(this.h=new hc(d));this.i.g=function(e,f){wc(c,e,f,c.i)};this.h&&(this.h.g=function(e,f){c.h&&wc(c,e,f,c.h)})}function xc(a,b,c,d){a=Zb(a.j,d).h;'media'===b||'video'===b?$a(a,c,d):(c={type:b,R:d,G:c},a.j.push(c),Za(a,b,c))}function yc(a,b,c,d){a=Zb(a.j,d);Yb(a,b,c,d)}
function zc(a,b,c,d){xb(a.g,b,c,d)}uc.prototype.setInterval=function(a,b){return this.g.setInterval(a,b)};uc.prototype.clearInterval=function(a){this.g.clearInterval(a)};function Ac(a,b,c,d){wb(a.g,'downloadJavaScriptResource')(b,c,d)}
function wc(a,b,c,d){function e(){var B=new W(f,'response',h,oc(h,w.apply(0,arguments)));d.h(B,c)}var f=b.h,g=b.method,h=b.version;b=pc(h,b.g);if(null!=gc(g,'VerificationService.')){g=gc(g,'VerificationService.');try{switch(g){case 'addEventListener':var k=p(b),l=k.next().value,m=k.next().value||Bc(c);xc(a,l,e,m);break;case 'addSessionListener':var t=p(b),I=t.next().value,ba=t.next().value||Bc(c);yc(a,e,I,ba);break;case 'sendUrl':var R=p(b).next().value;zc(a,R,function(){return e(!0)},function(){return e(!1)});
break;case 'setTimeout':var F=p(b),Fc=F.next().value,Gc=F.next().value;a.l[Fc]=ub(a.g,'setTimeout')(e,Gc);break;case 'clearTimeout':var Hc=p(b).next().value;vb(a.g,a.l[Hc]);break;case 'setInterval':var Rb=p(b),Ic=Rb.next().value,Jc=Rb.next().value;a.m[Ic]=a.setInterval(e,Jc);break;case 'clearInterval':var Kc=p(b).next().value;a.clearInterval(a.m[Kc]);break;case 'injectJavaScriptResource':var Lc=p(b).next().value;Ac(a,Lc,function(B){return e(!0,B)},function(){return e(!1)});break;case 'getVersion':e('1.5.2-iab4528')}}catch(B){d.h(new W(f,
'error',h,'\n              name: '+B.name+'\n              message: '+B.message+'\n              filename: '+B.filename+'\n              lineNumber: '+B.lineNumber+'\n              columnNumber: '+B.columnNumber+'\n              stack: '+B.stack+'\n              toString(): '+B.toString()+'\n          '),c)}}}
function Bc(a){for(var b=Na().h,c=p(b.keys()),d=c.next();!d.done;d=c.next()){d=d.value;var e=b.get(d);if(e){if(e.contentWindow===a)return d;try{if(e.contentWindow.Object.prototype.isPrototypeOf(a))return d}catch(f){}}}};function Cc(a){var b={};return(b.app='omid_v1_present_app',b.web='omid_v1_present_web',b)[a]}function Dc(a,b){a.document.write('<iframe style="display:none" id="'+(b+'" name="'+b+'" sandbox></iframe>'))}function Ec(a,b){var c=a.document.createElement('iframe');c.id=b;c.name=b;c.style.display='none';c.sandbox='';a.document.body.appendChild(c)}
function Mc(a,b){var c=new MutationObserver(function(d){d.forEach(function(e){'BODY'===e.addedNodes[0].nodeName&&(e=Cc(b),Ec(a,'omid_v1_present'),Ec(a,e),c.disconnect())})});c.observe(a.document.documentElement,{childList:!0})};var vc=new tb,kc=new function(){this.i=vc;this.h=new Map;this.g=$b(this)},rc=new jc;new uc;J.omidBridge=new Z;new qc;(function(a,b){a.frames&&a.document&&!['omid_v1_present','omid_v1_present_web','omid_v1_present_app'].some(function(c){return!!a.frames[c]})&&(null==a.document.body&&'MutationObserver'in a?Mc(a,b):(b=Cc(b),a.document.body?(Ec(a,'omid_v1_present'),Ec(a,b)):(Dc(a,'omid_v1_present'),Dc(a,b))))})(J,'app');
}).call(this, this);

