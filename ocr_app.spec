# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# مسیر فایل اصلی
main_script = 'ocr_app.py'

# فایل‌های اضافی که باید کپی شوند
added_files = [
    ('adb.exe', '.'),
    ('tesseract.exe', '.'),
    ('tessdata', 'tessdata'),
]

# کتابخانه‌های مخفی
hiddenimports = [
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'cv2',
    'numpy',
    'sqlite3',
    'threading',
    'subprocess',
    'datetime',
    'pathlib',
    'json',
    'os',
    'sys',
    'time',
    'random',
    'concurrent.futures',
    'psutil'
]

# تنظیمات Analysis
a = Analysis(
    [main_script],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# حذف فایل‌های غیرضروری
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# تنظیمات EXE
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='OCR_Kepithor_App',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # بدون console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico' if os.path.exists('app_icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)

# تنظیمات اضافی برای Windows
if sys.platform == 'win32':
    exe.manifest = """
    <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
    <assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
        <assemblyIdentity
            version="*******"
            processorArchitecture="*"
            name="OCR Kepithor App"
            type="win32"
        />
        <description>OCR Application for Kepithor Rewards</description>
        <dependency>
            <dependentAssembly>
                <assemblyIdentity
                    type="win32"
                    name="Microsoft.Windows.Common-Controls"
                    version="*******"
                    processorArchitecture="*"
                    publicKeyToken="6595b64144ccf1df"
                    language="*"
                />
            </dependentAssembly>
        </dependency>
        <application xmlns="urn:schemas-microsoft-com:asm.v3">
            <windowsSettings>
                <dpiAware xmlns="http://schemas.microsoft.com/SMI/2005/WindowsSettings">true</dpiAware>
                <dpiAwareness xmlns="http://schemas.microsoft.com/SMI/2016/WindowsSettings">PerMonitorV2</dpiAwareness>
            </windowsSettings>
        </application>
    </assembly>
    """
