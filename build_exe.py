#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Build Script for OCR App
اسکریپت ساخت فایل EXE برای OCR App
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """بررسی نیازمندی‌ها"""
    print("🔍 Checking requirements...")
    
    # بررسی فایل اصلی
    if not os.path.exists('ocr_app.py'):
        print("❌ ocr_app.py not found!")
        return False
    
    # بررسی PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller found")
    except ImportError:
        print("❌ PyInstaller not found!")
        print("Installing PyInstaller...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
    
    # بررسی کتابخانه‌های مورد نیاز
    required_packages = ['tkinter', 'PIL', 'cv2', 'numpy', 'psutil']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'PIL':
                from PIL import Image
            elif package == 'cv2':
                import cv2
            elif package == 'numpy':
                import numpy
            elif package == 'psutil':
                import psutil
            
            print(f"✅ {package} found")
        except ImportError:
            print(f"❌ {package} missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Installing missing packages: {missing_packages}")
        for package in missing_packages:
            if package == 'PIL':
                package = 'Pillow'
            elif package == 'cv2':
                package = 'opencv-python'
            
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
    
    return True

def create_version_info():
    """ایجاد فایل اطلاعات نسخه"""
    version_info = """
# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Kepithor OCR Solutions'),
        StringStruct(u'FileDescription', u'OCR Application for Kepithor Rewards'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'OCR_Kepithor_App'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024'),
        StringStruct(u'OriginalFilename', u'OCR_Kepithor_App.exe'),
        StringStruct(u'ProductName', u'Kepithor OCR App'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
"""
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✅ Version info created")

def create_icon():
    """ایجاد آیکون برنامه"""
    try:
        from PIL import Image, ImageDraw
        
        # ایجاد آیکون ساده
        size = (256, 256)
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # رسم آیکون OCR
        draw.ellipse([20, 20, 236, 236], fill='#4CAF50', outline='#2E7D32', width=4)
        draw.text((128, 100), "OCR", fill='white', anchor='mm', font_size=48)
        draw.text((128, 160), "📱", fill='white', anchor='mm', font_size=32)
        
        # ذخیره به عنوان ICO
        img.save('app_icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print("✅ Icon created")
        
    except Exception as e:
        print(f"⚠️ Could not create icon: {e}")

def clean_build():
    """پاک کردن فایل‌های build قبلی"""
    print("🧹 Cleaning previous build...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ Removed {dir_name}")

def build_exe():
    """ساخت فایل EXE"""
    print("🔨 Building EXE...")
    
    try:
        # اجرای PyInstaller
        cmd = [
            'pyinstaller',
            '--onefile',
            '--windowed',
            '--name=OCR_Kepithor_App',
            '--distpath=dist',
            '--workpath=build',
            '--specpath=.',
            '--clean',
            '--noconfirm'
        ]
        
        # اضافه کردن آیکون
        if os.path.exists('app_icon.ico'):
            cmd.extend(['--icon=app_icon.ico'])
        
        # اضافه کردن version info
        if os.path.exists('version_info.txt'):
            cmd.extend(['--version-file=version_info.txt'])
        
        # اضافه کردن فایل‌های اضافی
        additional_files = [
            ('adb.exe', '.'),
            ('tesseract.exe', '.'),
        ]
        
        for src, dst in additional_files:
            if os.path.exists(src):
                cmd.extend(['--add-data', f'{src};{dst}'])
        
        # اضافه کردن فولدرها
        if os.path.exists('tessdata'):
            cmd.extend(['--add-data', 'tessdata;tessdata'])
        
        # فایل اصلی
        cmd.append('ocr_app.py')
        
        print(f"Running: {' '.join(cmd)}")
        
        # اجرای دستور
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ EXE built successfully!")
            
            # بررسی فایل خروجی
            exe_path = Path('dist/OCR_Kepithor_App.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 EXE file: {exe_path}")
                print(f"📊 Size: {size_mb:.1f} MB")
                return True
            else:
                print("❌ EXE file not found!")
                return False
        else:
            print("❌ Build failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def copy_dependencies():
    """کپی فایل‌های وابسته"""
    print("📋 Copying dependencies...")
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ Dist directory not found!")
        return
    
    # فایل‌های مورد نیاز
    dependencies = [
        'adb.exe',
        'tesseract.exe',
        'tessdata'
    ]
    
    for dep in dependencies:
        if os.path.exists(dep):
            if os.path.isfile(dep):
                shutil.copy2(dep, dist_dir)
                print(f"✅ Copied {dep}")
            elif os.path.isdir(dep):
                shutil.copytree(dep, dist_dir / dep, dirs_exist_ok=True)
                print(f"✅ Copied {dep} directory")

def create_installer():
    """ایجاد installer"""
    print("📦 Creating installer package...")
    
    try:
        # ایجاد فولدر installer
        installer_dir = Path('installer')
        installer_dir.mkdir(exist_ok=True)
        
        # کپی فایل‌ها
        dist_dir = Path('dist')
        if dist_dir.exists():
            for item in dist_dir.iterdir():
                if item.is_file():
                    shutil.copy2(item, installer_dir)
                elif item.is_dir():
                    shutil.copytree(item, installer_dir / item.name, dirs_exist_ok=True)
        
        # ایجاد README
        readme_content = """
# OCR Kepithor App

## نصب و راه‌اندازی:

1. فایل OCR_Kepithor_App.exe را اجرا کنید
2. اگر Windows Defender هشدار داد، "More info" و سپس "Run anyway" را کلیک کنید
3. برنامه آماده استفاده است!

## فایل‌های مورد نیاز:
- OCR_Kepithor_App.exe (فایل اصلی برنامه)
- adb.exe (برای اتصال به شبیه‌سازها)
- tesseract.exe (برای OCR)
- tessdata/ (داده‌های OCR)

## سیستم مورد نیاز:
- Windows 10/11
- حداقل 4GB RAM
- 1GB فضای خالی

## پشتیبانی:
در صورت بروز مشکل، فایل‌های log را بررسی کنید.
"""
        
        with open(installer_dir / 'README.txt', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✅ Installer package created in: {installer_dir}")
        
    except Exception as e:
        print(f"❌ Installer creation error: {e}")

def main():
    """تابع اصلی"""
    print("🚀 OCR App EXE Builder")
    print("=" * 50)
    
    try:
        # بررسی نیازمندی‌ها
        if not check_requirements():
            return False
        
        # پاک کردن build قبلی
        clean_build()
        
        # ایجاد فایل‌های اضافی
        create_version_info()
        create_icon()
        
        # ساخت EXE
        if build_exe():
            # کپی وابستگی‌ها
            copy_dependencies()
            
            # ایجاد installer
            create_installer()
            
            print("\n🎉 Build completed successfully!")
            print("📁 EXE file: dist/OCR_Kepithor_App.exe")
            print("📦 Installer: installer/")
            
            return True
        else:
            print("\n❌ Build failed!")
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

if __name__ == "__main__":
    main()
