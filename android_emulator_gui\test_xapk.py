#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
XAPK Test Script
تست نصب فایل XAPK Kepithor
"""

import os
import sys
from pathlib import Path
from android_device import AndroidDevice

def test_xapk_extraction():
    """تست استخراج XAPK"""
    print("🧪 Testing XAPK Extraction")
    print("=" * 40)
    
    # جستجوی فایل XAPK
    xapk_files = [
        "Kepithor Rewards_159_APKPure.xapk",
        "../Kepithor Rewards_159_APKPure.xapk",
        "../../Kepithor Rewards_159_APKPure.xapk"
    ]
    
    xapk_path = None
    for path in xapk_files:
        if os.path.exists(path):
            xapk_path = path
            break
    
    if not xapk_path:
        print("❌ XAPK file not found!")
        print("Please make sure 'Kepithor Rewards_159_APKPure.xapk' is in the current directory")
        return False
    
    print(f"✅ Found XAPK: {xapk_path}")
    
    # ایجاد دستگاه تست
    device = AndroidDevice(device_id=999)
    
    try:
        # تست استخراج XAPK
        print("\n1. Testing XAPK extraction...")
        extracted_data = device.extract_xapk(xapk_path)
        
        if extracted_data:
            print("✅ XAPK extracted successfully!")
            print(f"   Main APK: {extracted_data['main_apk']}")
            print(f"   Extract Dir: {extracted_data['extract_dir']}")
            
            if extracted_data['app_info']:
                app_info = extracted_data['app_info']
                print(f"   Package: {app_info['package_name']}")
                print(f"   Name: {app_info['app_name']}")
                print(f"   Version: {app_info['version_name']}")
            
            if extracted_data['obb_files']:
                print(f"   OBB Files: {len(extracted_data['obb_files'])}")
                for obb in extracted_data['obb_files']:
                    print(f"     - {obb.name}")
            
            return True
        else:
            print("❌ XAPK extraction failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_xapk_installation():
    """تست نصب XAPK"""
    print("\n🧪 Testing XAPK Installation")
    print("=" * 40)
    
    # جستجوی فایل XAPK
    xapk_files = [
        "Kepithor Rewards_159_APKPure.xapk",
        "../Kepithor Rewards_159_APKPure.xapk",
        "../../Kepithor Rewards_159_APKPure.xapk"
    ]
    
    xapk_path = None
    for path in xapk_files:
        if os.path.exists(path):
            xapk_path = path
            break
    
    if not xapk_path:
        print("❌ XAPK file not found!")
        return False
    
    # ایجاد دستگاه تست
    device = AndroidDevice(device_id=998)
    
    try:
        # راه‌اندازی دستگاه
        print("1. Starting test device...")
        if not device.start_device():
            print("❌ Failed to start device")
            return False
        
        print("✅ Device started")
        
        # نصب XAPK
        print("2. Installing XAPK...")
        if device.install_apk(xapk_path):
            print("✅ XAPK installed successfully!")
            
            # بررسی نصب
            if 'com.KepithorStudios.KKTFaucet' in device.installed_apps:
                print("✅ Kepithor detected in installed apps")
                
                # تست اجرای Kepithor
                print("3. Testing Kepithor launch...")
                if device.launch_kepithor():
                    print("✅ Kepithor launched successfully!")
                    
                    # نگه داشتن برای مشاهده
                    input("\nPress Enter to close device...")
                    
                else:
                    print("❌ Failed to launch Kepithor")
            else:
                print("❌ Kepithor not found in installed apps")
        else:
            print("❌ XAPK installation failed!")
            return False
        
        # بستن دستگاه
        device.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_multiple_devices():
    """تست نصب روی چندین دستگاه"""
    print("\n🧪 Testing Multiple Devices Installation")
    print("=" * 40)
    
    # جستجوی فایل XAPK
    xapk_files = [
        "Kepithor Rewards_159_APKPure.xapk",
        "../Kepithor Rewards_159_APKPure.xapk",
        "../../Kepithor Rewards_159_APKPure.xapk"
    ]
    
    xapk_path = None
    for path in xapk_files:
        if os.path.exists(path):
            xapk_path = path
            break
    
    if not xapk_path:
        print("❌ XAPK file not found!")
        return False
    
    from emulator_farm import EmulatorFarm
    
    # ایجاد مزرعه با 3 دستگاه
    farm = EmulatorFarm(max_devices=3)
    
    try:
        # راه‌اندازی دستگاه‌ها
        print("1. Starting 3 test devices...")
        successful = farm.start_all_devices()
        
        if successful > 0:
            print(f"✅ {successful} devices started")
            
            # نصب XAPK
            print("2. Installing XAPK on all devices...")
            installed = farm.install_apk_on_all(xapk_path)
            
            if installed > 0:
                print(f"✅ XAPK installed on {installed} devices")
                
                # اجرای Kepithor
                print("3. Launching Kepithor on all devices...")
                launched = farm.launch_kepithor_on_all()
                
                if launched > 0:
                    print(f"✅ Kepithor launched on {launched} devices")
                    
                    # نمایش آمار
                    farm.print_status()
                    
                    # نگه داشتن برای مشاهده
                    input("\nPress Enter to shutdown all devices...")
                else:
                    print("❌ Failed to launch Kepithor")
            else:
                print("❌ Failed to install XAPK")
        else:
            print("❌ No devices started")
        
        # توقف همه دستگاه‌ها
        farm.stop_all_devices()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """منوی اصلی تست"""
    print("🧪 XAPK Test Suite for Kepithor")
    print("=" * 50)
    print("1. Test XAPK extraction")
    print("2. Test XAPK installation (single device)")
    print("3. Test multiple devices installation")
    print("4. Run all tests")
    print("5. Exit")
    
    while True:
        try:
            choice = input("\nSelect test (1-5): ").strip()
            
            if choice == "1":
                test_xapk_extraction()
            elif choice == "2":
                test_xapk_installation()
            elif choice == "3":
                test_multiple_devices()
            elif choice == "4":
                print("🚀 Running all tests...")
                test_xapk_extraction()
                test_xapk_installation()
                test_multiple_devices()
                print("🎉 All tests completed!")
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice!")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
