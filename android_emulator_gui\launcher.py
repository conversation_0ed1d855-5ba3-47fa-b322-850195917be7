#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Android Emulator Farm Launcher
راه‌انداز مزرعه شبیه‌ساز اندروید - انتخاب نوع شبیه‌ساز
"""

import os
import sys
import subprocess
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox

def check_requirements():
    """بررسی نیازمندی‌ها"""
    print("🔍 Checking requirements...")
    
    required_packages = [
        'tkinter',
        'PIL',
        'psutil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'PIL':
                from PIL import Image, ImageTk
            elif package == 'psutil':
                import psutil
            
            print(f"✅ {package} - OK")
            
        except ImportError:
            print(f"❌ {package} - Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        
        for package in missing_packages:
            if package == 'PIL':
                package = 'Pillow'
            
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ {package} installed successfully")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
                return False
    
    return True

def check_android_sdk():
    """بررسی وجود Android SDK"""
    possible_sdk_paths = [
        os.path.expanduser("~/Android/Sdk"),
        os.path.expanduser("~/AppData/Local/Android/Sdk"),
        "C:/Android/Sdk",
        "/opt/android-sdk",
        "/usr/local/android-sdk"
    ]
    
    for path in possible_sdk_paths:
        if os.path.exists(path):
            print(f"✅ Android SDK found: {path}")
            return True
    
    print("⚠️ Android SDK not found - will be downloaded automatically")
    return False

class EmulatorTypeSelectorGUI:
    """رابط انتخاب نوع شبیه‌ساز"""
    
    def __init__(self):
        """راه‌اندازی رابط انتخاب"""
        self.selected_type = None
        self.setup_gui()
    
    def setup_gui(self):
        """ایجاد رابط گرافیکی"""
        self.root = tk.Tk()
        self.root.title("🤖 Android Emulator Farm - انتخاب نوع شبیه‌ساز")
        self.root.geometry("600x500")
        self.root.configure(bg="#1a1a1a")
        self.root.resizable(False, False)
        
        # فریم اصلی
        main_frame = tk.Frame(self.root, bg="#1a1a1a")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # عنوان
        title_label = tk.Label(
            main_frame,
            text="🤖 Android Emulator Farm",
            font=("Arial", 20, "bold"),
            bg="#1a1a1a",
            fg="#ffffff"
        )
        title_label.pack(pady=10)
        
        subtitle_label = tk.Label(
            main_frame,
            text="انتخاب نوع شبیه‌ساز",
            font=("Arial", 14),
            bg="#1a1a1a",
            fg="#cccccc"
        )
        subtitle_label.pack(pady=5)
        
        # فریم انتخاب‌ها
        options_frame = tk.Frame(main_frame, bg="#1a1a1a")
        options_frame.pack(fill=tk.BOTH, expand=True, pady=20)
        
        # گزینه 1: شبیه‌سازهای واقعی
        self.create_option_card(
            options_frame,
            "🔥 شبیه‌سازهای واقعی اندروید",
            [
                "✅ نصب واقعی APK ها",
                "✅ اجرای واقعی Kepithor Rewards",
                "✅ استفاده از Android SDK",
                "✅ عملکرد 100% واقعی",
                "⚠️ نیاز به Android SDK",
                "⚠️ مصرف بالای منابع",
                "📊 توصیه: 1-10 دستگاه"
            ],
            "#4CAF50",
            lambda: self.select_type("real")
        )
        
        # گزینه 2: شبیه‌سازهای فیک
        self.create_option_card(
            options_frame,
            "⚡ شبیه‌سازهای سریع (فیک)",
            [
                "✅ راه‌اندازی سریع",
                "✅ مصرف کم منابع",
                "✅ تا 80 دستگاه همزمان",
                "✅ رابط گرافیکی زیبا",
                "⚠️ شبیه‌سازی فیک",
                "⚠️ عدم نصب واقعی APK",
                "📊 توصیه: تست و نمایش"
            ],
            "#2196F3",
            lambda: self.select_type("fake")
        )
        
        # دکمه‌های پایین
        buttons_frame = tk.Frame(main_frame, bg="#1a1a1a")
        buttons_frame.pack(fill=tk.X, pady=20)
        
        # دکمه تست سیستم
        test_btn = tk.Button(
            buttons_frame,
            text="🔧 تست سیستم",
            command=self.test_system,
            font=("Arial", 10),
            bg="#FF9800",
            fg="white",
            width=15,
            relief=tk.FLAT
        )
        test_btn.pack(side=tk.LEFT, padx=5)
        
        # دکمه خروج
        exit_btn = tk.Button(
            buttons_frame,
            text="🚪 خروج",
            command=self.root.quit,
            font=("Arial", 10),
            bg="#f44336",
            fg="white",
            width=15,
            relief=tk.FLAT
        )
        exit_btn.pack(side=tk.RIGHT, padx=5)
    
    def create_option_card(self, parent, title, features, color, command):
        """ایجاد کارت انتخاب"""
        # فریم کارت
        card_frame = tk.Frame(parent, bg="#2a2a2a", relief=tk.RAISED, bd=2)
        card_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان
        title_label = tk.Label(
            card_frame,
            text=title,
            font=("Arial", 14, "bold"),
            bg="#2a2a2a",
            fg=color
        )
        title_label.pack(pady=10)
        
        # ویژگی‌ها
        features_frame = tk.Frame(card_frame, bg="#2a2a2a")
        features_frame.pack(fill=tk.BOTH, expand=True, padx=20)
        
        for feature in features:
            feature_label = tk.Label(
                features_frame,
                text=feature,
                font=("Arial", 10),
                bg="#2a2a2a",
                fg="#ffffff",
                anchor=tk.W
            )
            feature_label.pack(fill=tk.X, pady=2)
        
        # دکمه انتخاب
        select_btn = tk.Button(
            card_frame,
            text="انتخاب این گزینه",
            command=command,
            font=("Arial", 12, "bold"),
            bg=color,
            fg="white",
            width=20,
            height=2,
            relief=tk.FLAT
        )
        select_btn.pack(pady=15)
    
    def select_type(self, emulator_type):
        """انتخاب نوع شبیه‌ساز"""
        self.selected_type = emulator_type
        
        if emulator_type == "real":
            # بررسی Android SDK
            if not check_android_sdk():
                result = messagebox.askyesno(
                    "Android SDK",
                    "Android SDK یافت نشد!\n\nآیا می‌خواهید به صورت خودکار دانلود شود؟\n(حدود 500MB)"
                )
                if not result:
                    return
            
            # تأیید انتخاب
            result = messagebox.askyesno(
                "تأیید انتخاب",
                "شبیه‌سازهای واقعی اندروید انتخاب شد.\n\n"
                "این گزینه:\n"
                "• APK ها را به صورت واقعی نصب می‌کند\n"
                "• منابع سیستم بیشتری مصرف می‌کند\n"
                "• زمان راه‌اندازی بیشتری دارد\n\n"
                "ادامه می‌دهید؟"
            )
            if result:
                self.launch_real_emulators()
        
        elif emulator_type == "fake":
            # تأیید انتخاب
            result = messagebox.askyesno(
                "تأیید انتخاب",
                "شبیه‌سازهای سریع (فیک) انتخاب شد.\n\n"
                "این گزینه:\n"
                "• شبیه‌سازی سریع و سبک\n"
                "• تا 80 دستگاه همزمان\n"
                "• عدم نصب واقعی APK\n\n"
                "ادامه می‌دهید؟"
            )
            if result:
                self.launch_fake_emulators()
    
    def launch_real_emulators(self):
        """راه‌اندازی شبیه‌سازهای واقعی"""
        try:
            self.root.destroy()
            
            print("🚀 Launching Real Android Emulators...")
            from main_gui import EmulatorFarmGUI
            
            gui = EmulatorFarmGUI(use_real_emulators=True)
            gui.run()
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در راه‌اندازی شبیه‌سازهای واقعی:\n{e}")
    
    def launch_fake_emulators(self):
        """راه‌اندازی شبیه‌سازهای فیک"""
        try:
            self.root.destroy()
            
            print("🚀 Launching Fake Emulators...")
            from main_gui import EmulatorFarmGUI
            
            gui = EmulatorFarmGUI(use_real_emulators=False)
            gui.run()
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در راه‌اندازی شبیه‌سازهای فیک:\n{e}")
    
    def test_system(self):
        """تست سیستم"""
        try:
            import psutil
            
            # اطلاعات سیستم
            cpu_count = psutil.cpu_count()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
            
            # بررسی Android SDK
            sdk_status = "✅ یافت شد" if check_android_sdk() else "❌ یافت نشد"
            
            report = f"""
🖥️ گزارش سیستم:

💻 CPU: {cpu_count} هسته
💾 RAM: {memory.total / (1024**3):.1f} GB (آزاد: {memory.available / (1024**3):.1f} GB)
💽 فضای دیسک: {disk.free / (1024**3):.1f} GB آزاد
🤖 Android SDK: {sdk_status}

📊 توصیه‌ها:
• شبیه‌سازهای واقعی: {min(10, cpu_count)} دستگاه
• شبیه‌سازهای فیک: {min(80, cpu_count * 20)} دستگاه

💡 برای عملکرد بهتر:
• RAM بیشتر از 8GB توصیه می‌شود
• CPU بیشتر از 4 هسته توصیه می‌شود
            """
            
            messagebox.showinfo("گزارش سیستم", report)
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در تست سیستم:\n{e}")
    
    def run(self):
        """اجرای رابط"""
        self.root.mainloop()

def main():
    """تابع اصلی"""
    print("🤖 Android Emulator Farm Launcher")
    print("=" * 50)
    
    # بررسی نیازمندی‌ها
    if not check_requirements():
        print("❌ Requirements check failed!")
        input("Press Enter to exit...")
        return
    
    # ایجاد فولدرهای مورد نیاز
    directories = ["devices", "android_farm", "logs", "screenshots", "temp_xapk_extraction"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    # راه‌اندازی رابط انتخاب
    try:
        selector = EmulatorTypeSelectorGUI()
        selector.run()
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
