#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
APK Analyzer for Kepithor Rewards
تحلیل فایل APK برای استخراج API endpoints و اطلاعات مهم
"""

import os
import sys
import json
import requests
import uuid
import random
import time
from datetime import datetime
from androguard.misc import AnalyzeAPK
import zipfile
import xml.etree.ElementTree as ET

class APKAnalyzer:
    def __init__(self, apk_path):
        """Initialize APK analyzer"""
        self.apk_path = apk_path
        self.apk_obj = None
        self.dex_files = []
        self.analysis_obj = None
        self.manifest_info = {}
        self.api_endpoints = []
        self.network_urls = []

    def load_apk(self):
        """Load and parse APK file"""
        try:
            print(f"📱 Loading APK: {self.apk_path}")

            # Load APK using androg<PERSON>'s AnalyzeAPK
            self.apk_obj, self.dex_files, self.analysis_obj = AnalyzeAPK(self.apk_path)

            print("✅ APK loaded successfully!")
            return True

        except Exception as e:
            print(f"❌ Error loading APK: {e}")
            return False
    
    def extract_manifest_info(self):
        """Extract information from AndroidManifest.xml"""
        try:
            print("📋 Extracting manifest information...")
            
            # Get package name
            self.manifest_info['package_name'] = self.apk_obj.get_package()
            
            # Get app name
            self.manifest_info['app_name'] = self.apk_obj.get_app_name()
            
            # Get version info
            self.manifest_info['version_name'] = self.apk_obj.get_androidversion_name()
            self.manifest_info['version_code'] = self.apk_obj.get_androidversion_code()
            
            # Get permissions
            self.manifest_info['permissions'] = self.apk_obj.get_permissions()
            
            # Get activities
            self.manifest_info['activities'] = self.apk_obj.get_activities()
            
            # Get services
            self.manifest_info['services'] = self.apk_obj.get_services()
            
            # Get receivers
            self.manifest_info['receivers'] = self.apk_obj.get_receivers()
            
            print("✅ Manifest information extracted!")
            return True
            
        except Exception as e:
            print(f"❌ Error extracting manifest: {e}")
            return False
    
    def find_network_endpoints(self):
        """Find network endpoints and URLs in the code"""
        try:
            print("🌐 Searching for network endpoints...")
            
            # Common patterns to search for
            url_patterns = [
                'http://',
                'https://',
                'api/',
                '/api/',
                '.com',
                '.net',
                '.org',
                'kepithor',
                'reward',
                'login',
                'auth',
                'token',
                'balance',
                'watch',
                'ad',
                'claim'
            ]
            
            # Search in all classes
            for dex in self.dex_files:
                for class_def in dex.get_classes():
                    class_name = class_def.get_name()

                    # Search in methods
                    for method in class_def.get_methods():
                        method_name = method.get_name()

                        # Get method code
                        try:
                            code = method.get_code()
                            if code:
                                # Convert bytecode to string representation
                                method_str = str(code)

                                # Search for URL patterns
                                for pattern in url_patterns:
                                    if pattern.lower() in method_str.lower():
                                        endpoint_info = {
                                            'class': class_name,
                                            'method': method_name,
                                            'pattern': pattern,
                                            'context': method_str[:200] + '...' if len(method_str) > 200 else method_str
                                        }
                                        self.api_endpoints.append(endpoint_info)

                        except Exception as e:
                            continue
            
            print(f"✅ Found {len(self.api_endpoints)} potential endpoints!")
            return True
            
        except Exception as e:
            print(f"❌ Error finding endpoints: {e}")
            return False
    
    def extract_strings(self):
        """Extract all strings from APK"""
        try:
            print("📝 Extracting strings...")
            
            strings = []
            
            # Extract strings from DEX files
            for dex in self.dex_files:
                for string in dex.get_strings():
                    string_value = string
                    if string_value:
                        strings.append(string_value)
            
            # Filter interesting strings
            interesting_strings = []
            keywords = ['http', 'api', 'token', 'key', 'secret', 'login', 'auth', 'kepithor', 'reward', 'balance', 'ad', 'watch', 'claim']
            
            for s in strings:
                for keyword in keywords:
                    if keyword.lower() in s.lower():
                        interesting_strings.append(s)
                        break
            
            print(f"✅ Found {len(interesting_strings)} interesting strings!")
            return interesting_strings
            
        except Exception as e:
            print(f"❌ Error extracting strings: {e}")
            return []
    
    def generate_report(self):
        """Generate analysis report"""
        try:
            print("📊 Generating analysis report...")
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'apk_path': self.apk_path,
                'manifest_info': self.manifest_info,
                'api_endpoints': self.api_endpoints,
                'strings': self.extract_strings()[:50]  # First 50 interesting strings
            }
            
            # Save report to file
            report_file = f"apk_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Report saved to: {report_file}")
            return report_file
            
        except Exception as e:
            print(f"❌ Error generating report: {e}")
            return None
    
    def analyze(self):
        """Run complete analysis"""
        print("🚀 Starting APK analysis...")
        
        if not self.load_apk():
            return False
            
        if not self.extract_manifest_info():
            return False
            
        self.find_network_endpoints()
        
        report_file = self.generate_report()
        
        print("🎯 Analysis complete!")
        print(f"📦 Package: {self.manifest_info.get('package_name', 'Unknown')}")
        print(f"📱 App Name: {self.manifest_info.get('app_name', 'Unknown')}")
        print(f"🔢 Version: {self.manifest_info.get('version_name', 'Unknown')}")
        print(f"🌐 Endpoints Found: {len(self.api_endpoints)}")
        
        return True

class DeviceSimulator:
    """Simulate Android device information"""
    
    @staticmethod
    def generate_device_info():
        """Generate random device information"""
        devices = [
            {"model": "SM-G973F", "brand": "Samsung", "device": "beyond1lte"},
            {"model": "Pixel 4", "brand": "Google", "device": "flame"},
            {"model": "OnePlus 8", "brand": "OnePlus", "device": "instantnoodle"},
            {"model": "Mi 10", "brand": "Xiaomi", "device": "umi"},
            {"model": "SM-A515F", "brand": "Samsung", "device": "a51"},
        ]
        
        device = random.choice(devices)
        android_versions = ["10", "11", "12", "13"]
        
        return {
            "device_id": str(uuid.uuid4()),
            "android_id": ''.join(random.choices('0123456789abcdef', k=16)),
            "model": device["model"],
            "brand": device["brand"],
            "device": device["device"],
            "version": random.choice(android_versions),
            "sdk_int": random.randint(29, 33),
            "screen_density": random.choice([2.0, 2.75, 3.0]),
            "screen_width": random.choice([1080, 1440]),
            "screen_height": random.choice([1920, 2560, 3040]),
            "manufacturer": device["brand"],
            "fingerprint": f"{device['brand']}/{device['device']}/{device['device']}:11/RKQ1.200826.002/2021010100:user/release-keys"
        }

def main():
    """Main function"""
    print("🔍 Kepithor APK Analyzer")
    print("=" * 50)
    
    # Check if APK file is provided
    if len(sys.argv) < 2:
        print("Usage: python apk_analyzer.py <path_to_apk>")
        return
    
    apk_path = sys.argv[1]
    
    if not os.path.exists(apk_path):
        print(f"❌ APK file not found: {apk_path}")
        return
    
    # Run analysis
    analyzer = APKAnalyzer(apk_path)
    success = analyzer.analyze()
    
    if success:
        print("\n🎉 Analysis completed successfully!")
        print("Next steps:")
        print("1. Review the generated report")
        print("2. Use mitmproxy to capture network traffic")
        print("3. Implement API simulation based on findings")
    else:
        print("\n❌ Analysis failed!")

if __name__ == "__main__":
    main()
