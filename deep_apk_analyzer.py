#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Deep APK Code Analyzer for Kepithor Rewards
تحلیل عمیق کد APK برای استخراج منطق برنامه
"""

import os
import sys
import json
import zipfile
import subprocess
import re
from datetime import datetime

class DeepAPKAnalyzer:
    """Deep analysis of APK to extract business logic"""
    
    def __init__(self, apk_path):
        """Initialize deep analyzer"""
        self.apk_path = apk_path
        self.extracted_dir = "kepithor_extracted"
        self.java_code = {}
        self.api_endpoints = []
        self.constants = {}
        self.methods = {}
        
    def extract_apk_completely(self):
        """Extract APK using multiple tools"""
        try:
            print("🔧 Extracting APK completely...")
            
            # Create extraction directory
            if os.path.exists(self.extracted_dir):
                import shutil
                shutil.rmtree(self.extracted_dir)
            os.makedirs(self.extracted_dir)
            
            # Method 1: Extract as ZIP
            with zipfile.ZipFile(self.apk_path, 'r') as zip_ref:
                zip_ref.extractall(f"{self.extracted_dir}/zip_content")
            
            # Method 2: Try to use jadx if available
            self.try_jadx_decompile()
            
            # Method 3: Manual DEX analysis
            self.analyze_dex_files()
            
            return True
            
        except Exception as e:
            print(f"❌ Error extracting APK: {e}")
            return False
    
    def try_jadx_decompile(self):
        """Try to decompile with jadx if available"""
        try:
            # Check if jadx is available
            result = subprocess.run(['jadx', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ JADX found, decompiling...")
                
                # Decompile APK
                jadx_output = f"{self.extracted_dir}/jadx_output"
                subprocess.run([
                    'jadx', 
                    '-d', jadx_output,
                    '--show-bad-code',
                    self.apk_path
                ], timeout=300)
                
                print("✅ JADX decompilation completed")
                return True
                
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.CalledProcessError):
            print("⚠️ JADX not available, using alternative methods")
            return False
    
    def analyze_dex_files(self):
        """Analyze DEX files manually"""
        try:
            print("🔍 Analyzing DEX files...")
            
            dex_dir = f"{self.extracted_dir}/zip_content"
            
            # Find all DEX files
            dex_files = []
            for file in os.listdir(dex_dir):
                if file.endswith('.dex'):
                    dex_files.append(os.path.join(dex_dir, file))
            
            print(f"📱 Found {len(dex_files)} DEX files")
            
            # Analyze each DEX file
            for dex_file in dex_files:
                self.extract_strings_from_dex(dex_file)
            
            return True
            
        except Exception as e:
            print(f"❌ Error analyzing DEX files: {e}")
            return False
    
    def extract_strings_from_dex(self, dex_file):
        """Extract strings from DEX file"""
        try:
            with open(dex_file, 'rb') as f:
                content = f.read()
            
            # Convert to string and find patterns
            content_str = content.decode('latin-1', errors='ignore')
            
            # Find URLs
            url_pattern = r'https?://[^\s<>"\']+[^\s<>"\'.,;!?]'
            urls = re.findall(url_pattern, content_str)
            
            for url in urls:
                if self.is_relevant_url(url):
                    self.api_endpoints.append(url)
                    print(f"🌐 Found URL: {url}")
            
            # Find API-like strings
            api_patterns = [
                r'/api/[^\s<>"\']+',
                r'/v\d+/[^\s<>"\']+',
                r'[a-zA-Z]+\.php[^\s<>"\']*',
                r'[a-zA-Z]+\.json[^\s<>"\']*'
            ]
            
            for pattern in api_patterns:
                matches = re.findall(pattern, content_str)
                for match in matches:
                    if match not in self.api_endpoints:
                        self.api_endpoints.append(match)
                        print(f"🎯 Found API endpoint: {match}")
            
            # Find constants and keys
            self.extract_constants(content_str)
            
        except Exception as e:
            print(f"❌ Error extracting from DEX: {e}")
    
    def is_relevant_url(self, url):
        """Check if URL is relevant to Kepithor"""
        relevant_keywords = [
            'kepithor', 'reward', 'faucet', 'api', 'auth', 'login',
            'balance', 'claim', 'watch', 'ad', 'point', 'earn'
        ]
        
        url_lower = url.lower()
        return any(keyword in url_lower for keyword in relevant_keywords)
    
    def extract_constants(self, content):
        """Extract constants and configuration values"""
        try:
            # Find potential API keys
            api_key_patterns = [
                r'["\']([A-Za-z0-9]{20,})["\']',  # Long alphanumeric strings
                r'api[_-]?key["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'secret["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in api_key_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    if len(match) > 10:  # Filter out short strings
                        self.constants[f'potential_key_{len(self.constants)}'] = match
                        print(f"🔑 Found potential key: {match[:20]}...")
            
            # Find version numbers
            version_pattern = r'version["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            versions = re.findall(version_pattern, content, re.IGNORECASE)
            for version in versions:
                self.constants['version'] = version
                print(f"📱 Found version: {version}")
            
        except Exception as e:
            print(f"❌ Error extracting constants: {e}")
    
    def analyze_java_code(self):
        """Analyze decompiled Java code if available"""
        try:
            jadx_dir = f"{self.extracted_dir}/jadx_output"
            
            if not os.path.exists(jadx_dir):
                print("⚠️ No Java code available")
                return False
            
            print("🔍 Analyzing Java code...")
            
            # Find Java files
            java_files = []
            for root, dirs, files in os.walk(jadx_dir):
                for file in files:
                    if file.endswith('.java'):
                        java_files.append(os.path.join(root, file))
            
            print(f"📄 Found {len(java_files)} Java files")
            
            # Analyze each Java file
            for java_file in java_files[:50]:  # Limit to first 50 files
                self.analyze_java_file(java_file)
            
            return True
            
        except Exception as e:
            print(f"❌ Error analyzing Java code: {e}")
            return False
    
    def analyze_java_file(self, java_file):
        """Analyze individual Java file"""
        try:
            with open(java_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Extract class name
            class_match = re.search(r'class\s+(\w+)', content)
            class_name = class_match.group(1) if class_match else 'Unknown'
            
            # Find methods
            method_pattern = r'(public|private|protected)?\s*(static)?\s*(\w+)\s+(\w+)\s*\([^)]*\)\s*\{'
            methods = re.findall(method_pattern, content)
            
            # Find HTTP requests
            http_patterns = [
                r'HttpURLConnection',
                r'OkHttp',
                r'Retrofit',
                r'\.post\(',
                r'\.get\(',
                r'\.put\(',
                r'\.delete\('
            ]
            
            has_http = any(re.search(pattern, content, re.IGNORECASE) for pattern in http_patterns)
            
            if has_http or 'api' in java_file.lower():
                self.java_code[class_name] = {
                    'file_path': java_file,
                    'methods': [method[3] for method in methods],  # Method names
                    'has_http': has_http,
                    'content_preview': content[:500]
                }
                
                print(f"📋 Analyzed class: {class_name} ({len(methods)} methods)")
            
        except Exception as e:
            print(f"❌ Error analyzing Java file {java_file}: {e}")
    
    def generate_analysis_report(self):
        """Generate comprehensive analysis report"""
        try:
            print("📊 Generating analysis report...")
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'apk_path': self.apk_path,
                'analysis_summary': {
                    'api_endpoints_found': len(self.api_endpoints),
                    'constants_found': len(self.constants),
                    'java_classes_analyzed': len(self.java_code)
                },
                'api_endpoints': self.api_endpoints,
                'constants': self.constants,
                'java_classes': {k: {
                    'methods': v['methods'],
                    'has_http': v['has_http']
                } for k, v in self.java_code.items()},
                'recommendations': self.generate_recommendations()
            }
            
            # Save report
            report_file = f"kepithor_deep_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Report saved to: {report_file}")
            return report_file
            
        except Exception as e:
            print(f"❌ Error generating report: {e}")
            return None
    
    def generate_recommendations(self):
        """Generate recommendations for Python implementation"""
        recommendations = []
        
        if self.api_endpoints:
            recommendations.append("✅ API endpoints found - can implement direct HTTP communication")
        else:
            recommendations.append("⚠️ No clear API endpoints found - may need network monitoring")
        
        if self.constants:
            recommendations.append("✅ Constants found - can use for authentication")
        else:
            recommendations.append("⚠️ No clear constants found - may need to capture from runtime")
        
        if self.java_code:
            recommendations.append("✅ Java code available - can reverse engineer business logic")
        else:
            recommendations.append("⚠️ No Java code available - limited analysis possible")
        
        return recommendations
    
    def run_complete_analysis(self):
        """Run complete analysis pipeline"""
        print("🚀 Starting deep APK analysis...")
        print("=" * 60)
        
        # Step 1: Extract APK
        if not self.extract_apk_completely():
            return False
        
        # Step 2: Analyze Java code
        self.analyze_java_code()
        
        # Step 3: Generate report
        report_file = self.generate_analysis_report()
        
        # Step 4: Print summary
        self.print_summary()
        
        return report_file
    
    def print_summary(self):
        """Print analysis summary"""
        print("\n" + "="*60)
        print("📊 DEEP ANALYSIS SUMMARY")
        print("="*60)
        print(f"🌐 API Endpoints: {len(self.api_endpoints)}")
        print(f"🔑 Constants: {len(self.constants)}")
        print(f"📋 Java Classes: {len(self.java_code)}")
        
        if self.api_endpoints:
            print("\n🎯 Key API Endpoints:")
            for endpoint in self.api_endpoints[:10]:
                print(f"  • {endpoint}")
        
        if self.constants:
            print("\n🔑 Key Constants:")
            for key, value in list(self.constants.items())[:5]:
                print(f"  • {key}: {str(value)[:50]}...")

def main():
    """Main function"""
    print("🔍 Deep Kepithor APK Analyzer")
    print("=" * 50)
    
    apk_path = "extracted_xapk/com.KepithorStudios.KKTFaucet.apk"
    
    if not os.path.exists(apk_path):
        print(f"❌ APK file not found: {apk_path}")
        return
    
    # Run analysis
    analyzer = DeepAPKAnalyzer(apk_path)
    report_file = analyzer.run_complete_analysis()
    
    if report_file:
        print(f"\n🎉 Analysis completed successfully!")
        print(f"📄 Report: {report_file}")
        print("\nNext steps:")
        print("1. Review the analysis report")
        print("2. Implement Python simulator based on findings")
        print("3. Create multi-threaded execution")
    else:
        print("\n❌ Analysis failed!")

if __name__ == "__main__":
    main()
