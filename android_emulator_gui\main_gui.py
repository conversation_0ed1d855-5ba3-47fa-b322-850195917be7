#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main GUI for Android Emulator Farm
رابط گرافیکی اصلی مزرعه شبیه‌ساز اندروید
"""

import os
import sys
import json
import time
import threading
from datetime import datetime
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk

from emulator_farm import EmulatorFarm

class EmulatorFarmGUI:
    """رابط گرافیکی مزرعه شبیه‌ساز"""
    
    def __init__(self, use_real_emulators=False):
        """راه‌اندازی رابط گرافیکی"""
        self.use_real_emulators = use_real_emulators

        if use_real_emulators:
            from real_emulator_manager import RealEmulatorManager
            self.farm = RealEmulatorManager(max_devices=10)
            self.emulator_type = "Real Android Emulators"
        else:
            from emulator_farm import EmulatorFarm
            self.farm = EmulatorFarm(max_devices=80)
            self.emulator_type = "Simulated Emulators"

        self.setup_gui()
        self.update_stats_timer()
        
    def setup_gui(self):
        """ایجاد رابط گرافیکی"""
        self.root = tk.Tk()
        self.root.title("🏭 Android Emulator Farm - 80 Devices")
        self.root.geometry("1200x800")
        self.root.configure(bg="#1a1a1a")
        
        # استایل
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        
        # فریم اصلی
        main_frame = tk.Frame(self.root, bg="#1a1a1a")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان
        title_text = f"🏭 مزرعه شبیه‌ساز اندروید - {self.emulator_type}"
        title_label = tk.Label(
            main_frame,
            text=title_text,
            font=("Arial", 18, "bold"),
            bg="#1a1a1a",
            fg="#ffffff"
        )
        title_label.pack(pady=10)
        
        # فریم کنترل‌ها
        self.create_control_panel(main_frame)
        
        # فریم آمار
        self.create_stats_panel(main_frame)
        
        # فریم لیست دستگاه‌ها
        self.create_devices_panel(main_frame)
        
        # نوار وضعیت
        self.create_status_bar(main_frame)
    
    def create_control_panel(self, parent):
        """ایجاد پنل کنترل"""
        control_frame = tk.LabelFrame(
            parent,
            text="🎮 کنترل مزرعه",
            font=("Arial", 12, "bold"),
            bg="#2a2a2a",
            fg="#ffffff",
            relief=tk.RAISED,
            bd=2
        )
        control_frame.pack(fill=tk.X, pady=5)
        
        # ردیف اول دکمه‌ها
        row1_frame = tk.Frame(control_frame, bg="#2a2a2a")
        row1_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # فریم تعداد دستگاه
        device_count_frame = tk.Frame(row1_frame, bg="#2a2a2a")
        device_count_frame.pack(side=tk.LEFT, padx=5)

        tk.Label(device_count_frame, text="تعداد:", bg="#2a2a2a", fg="white", font=("Arial", 9)).pack()
        self.device_count_var = tk.StringVar(value="10")
        self.device_count_entry = tk.Entry(
            device_count_frame,
            textvariable=self.device_count_var,
            width=5,
            font=("Arial", 10),
            justify=tk.CENTER
        )
        self.device_count_entry.pack()

        # دکمه راه‌اندازی
        self.start_all_btn = tk.Button(
            row1_frame,
            text="🚀 راه‌اندازی دستگاه‌ها",
            command=self.start_all_devices,
            font=("Arial", 11, "bold"),
            bg="#4CAF50",
            fg="white",
            width=25,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.start_all_btn.pack(side=tk.LEFT, padx=5)
        
        # دکمه توقف همه
        self.stop_all_btn = tk.Button(
            row1_frame,
            text="🛑 توقف همه دستگاه‌ها",
            command=self.stop_all_devices,
            font=("Arial", 11, "bold"),
            bg="#f44336",
            fg="white",
            width=30,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.stop_all_btn.pack(side=tk.LEFT, padx=5)
        
        # دکمه نصب APK/XAPK
        self.install_apk_btn = tk.Button(
            row1_frame,
            text="📦 نصب Kepithor (APK/XAPK)",
            command=self.install_apk,
            font=("Arial", 11, "bold"),
            bg="#FF9800",
            fg="white",
            width=30,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.install_apk_btn.pack(side=tk.LEFT, padx=5)
        
        # ردیف دوم دکمه‌ها
        row2_frame = tk.Frame(control_frame, bg="#2a2a2a")
        row2_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # دکمه اجرای Kepithor
        self.launch_kepithor_btn = tk.Button(
            row2_frame,
            text="💰 اجرای Kepithor روی همه",
            command=self.launch_kepithor,
            font=("Arial", 11, "bold"),
            bg="#2196F3",
            fg="white",
            width=25,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.launch_kepithor_btn.pack(side=tk.LEFT, padx=5)

        # دکمه بستن Kepithor
        self.close_kepithor_btn = tk.Button(
            row2_frame,
            text="❌ بستن Kepithor",
            command=self.close_kepithor,
            font=("Arial", 11, "bold"),
            bg="#E91E63",
            fg="white",
            width=20,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.close_kepithor_btn.pack(side=tk.LEFT, padx=5)
        
        # ردیف سوم دکمه‌ها
        row3_frame = tk.Frame(control_frame, bg="#2a2a2a")
        row3_frame.pack(fill=tk.X, padx=10, pady=5)

        # دکمه چیدمان کاشی‌وار
        self.tile_windows_btn = tk.Button(
            row3_frame,
            text="🔲 چیدمان کاشی‌وار",
            command=self.tile_windows,
            font=("Arial", 11, "bold"),
            bg="#FF5722",
            fg="white",
            width=20,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.tile_windows_btn.pack(side=tk.LEFT, padx=5)

        # دکمه تنظیمات
        self.settings_btn = tk.Button(
            row3_frame,
            text="⚙️ تنظیمات",
            command=self.show_settings,
            font=("Arial", 11, "bold"),
            bg="#9C27B0",
            fg="white",
            width=20,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.settings_btn.pack(side=tk.LEFT, padx=5)

        # دکمه گزارش
        self.report_btn = tk.Button(
            row3_frame,
            text="📊 گزارش عملکرد",
            command=self.show_report,
            font=("Arial", 11, "bold"),
            bg="#607D8B",
            fg="white",
            width=20,
            height=2,
            relief=tk.RAISED,
            bd=3
        )
        self.report_btn.pack(side=tk.LEFT, padx=5)
        
        # نوار پیشرفت
        self.progress_frame = tk.Frame(control_frame, bg="#2a2a2a")
        self.progress_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=400,
            mode='determinate'
        )
        self.progress_bar.pack(side=tk.LEFT, padx=5)
        
        self.progress_label = tk.Label(
            self.progress_frame,
            text="آماده",
            bg="#2a2a2a",
            fg="#ffffff",
            font=("Arial", 10)
        )
        self.progress_label.pack(side=tk.LEFT, padx=10)
    
    def create_stats_panel(self, parent):
        """ایجاد پنل آمار"""
        stats_frame = tk.LabelFrame(
            parent,
            text="📊 آمار سیستم",
            font=("Arial", 12, "bold"),
            bg="#2a2a2a",
            fg="#ffffff",
            relief=tk.RAISED,
            bd=2
        )
        stats_frame.pack(fill=tk.X, pady=5)
        
        # فریم آمار
        stats_content = tk.Frame(stats_frame, bg="#2a2a2a")
        stats_content.pack(fill=tk.X, padx=10, pady=5)
        
        # ستون اول
        col1_frame = tk.Frame(stats_content, bg="#2a2a2a")
        col1_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.total_devices_label = tk.Label(
            col1_frame,
            text="📱 کل دستگاه‌ها: 0",
            bg="#2a2a2a",
            fg="#ffffff",
            font=("Arial", 11, "bold")
        )
        self.total_devices_label.pack(anchor=tk.W, pady=2)
        
        self.running_devices_label = tk.Label(
            col1_frame,
            text="🟢 دستگاه‌های فعال: 0",
            bg="#2a2a2a",
            fg="#4CAF50",
            font=("Arial", 11, "bold")
        )
        self.running_devices_label.pack(anchor=tk.W, pady=2)
        
        # ستون دوم
        col2_frame = tk.Frame(stats_content, bg="#2a2a2a")
        col2_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.installed_apps_label = tk.Label(
            col2_frame,
            text="📦 APK نصب شده: 0",
            bg="#2a2a2a",
            fg="#FF9800",
            font=("Arial", 11, "bold")
        )
        self.installed_apps_label.pack(anchor=tk.W, pady=2)
        
        self.active_kepithor_label = tk.Label(
            col2_frame,
            text="💰 Kepithor فعال: 0",
            bg="#2a2a2a",
            fg="#2196F3",
            font=("Arial", 11, "bold")
        )
        self.active_kepithor_label.pack(anchor=tk.W, pady=2)
        
        # ستون سوم
        col3_frame = tk.Frame(stats_content, bg="#2a2a2a")
        col3_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.memory_label = tk.Label(
            col3_frame,
            text="💾 حافظه: 0%",
            bg="#2a2a2a",
            fg="#ffffff",
            font=("Arial", 11, "bold")
        )
        self.memory_label.pack(anchor=tk.W, pady=2)
        
        self.cpu_label = tk.Label(
            col3_frame,
            text="🔥 CPU: 0%",
            bg="#2a2a2a",
            fg="#ffffff",
            font=("Arial", 11, "bold")
        )
        self.cpu_label.pack(anchor=tk.W, pady=2)
    
    def create_devices_panel(self, parent):
        """ایجاد پنل لیست دستگاه‌ها"""
        devices_frame = tk.LabelFrame(
            parent,
            text="📱 لیست دستگاه‌ها",
            font=("Arial", 12, "bold"),
            bg="#2a2a2a",
            fg="#ffffff",
            relief=tk.RAISED,
            bd=2
        )
        devices_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # جدول دستگاه‌ها
        columns = ("ID", "IP Address", "Status", "Model", "Kepithor", "Memory")
        self.devices_tree = ttk.Treeview(
            devices_frame,
            columns=columns,
            show="headings",
            height=15
        )
        
        # تنظیم ستون‌ها
        for col in columns:
            self.devices_tree.heading(col, text=col)
            if col == "ID":
                self.devices_tree.column(col, width=50)
            elif col == "IP Address":
                self.devices_tree.column(col, width=120)
            elif col == "Status":
                self.devices_tree.column(col, width=80)
            elif col == "Model":
                self.devices_tree.column(col, width=150)
            elif col == "Kepithor":
                self.devices_tree.column(col, width=80)
            else:
                self.devices_tree.column(col, width=100)
        
        # نوار پیمایش
        scrollbar = ttk.Scrollbar(devices_frame, orient="vertical", command=self.devices_tree.yview)
        self.devices_tree.configure(yscrollcommand=scrollbar.set)
        
        # قرار دادن در فریم
        self.devices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # منوی راست کلیک
        self.create_context_menu()
    
    def create_context_menu(self):
        """ایجاد منوی راست کلیک"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="🚀 راه‌اندازی دستگاه", command=self.start_selected_device)
        self.context_menu.add_command(label="🛑 توقف دستگاه", command=self.stop_selected_device)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📦 نصب APK", command=self.install_apk_on_selected)
        self.context_menu.add_command(label="💰 اجرای Kepithor", command=self.launch_kepithor_on_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📊 نمایش اطلاعات", command=self.show_device_info)
        
        # اتصال به treeview
        self.devices_tree.bind("<Button-3>", self.show_context_menu)
    
    def create_status_bar(self, parent):
        """ایجاد نوار وضعیت"""
        self.status_bar = tk.Label(
            parent,
            text="آماده",
            bg="#333333",
            fg="#ffffff",
            font=("Arial", 10),
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def update_progress(self, value, text):
        """به‌روزرسانی نوار پیشرفت"""
        self.progress_var.set(value)
        self.progress_label.config(text=text)
        self.root.update_idletasks()
    
    def start_all_devices(self):
        """راه‌اندازی دستگاه‌ها"""
        try:
            device_count = int(self.device_count_var.get())
            if device_count <= 0 or device_count > 200:
                messagebox.showerror("خطا", "تعداد دستگاه باید بین 1 تا 200 باشد!")
                return
        except ValueError:
            messagebox.showerror("خطا", "لطفاً عدد معتبر وارد کنید!")
            return

        # به‌روزرسانی تعداد دستگاه در farm
        self.farm.max_devices = device_count

        self.start_all_btn.config(state="disabled", text="در حال راه‌اندازی...")
        self.status_bar.config(text=f"راه‌اندازی {device_count} دستگاه...")

        def start_thread():
            try:
                successful = self.farm.start_all_devices(
                    progress_callback=self.update_progress
                )

                self.root.after(0, lambda: self.update_button_state(
                    self.start_all_btn, "🚀 راه‌اندازی دستگاه‌ها", "normal"
                ))

                self.root.after(0, lambda: self.status_bar.config(
                    text=f"✅ {successful} دستگاه راه‌اندازی شد"
                ))

                self.root.after(0, lambda: messagebox.showinfo(
                    "اطلاعات", f"{successful} دستگاه با موفقیت راه‌اندازی شد"
                ))

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در راه‌اندازی: {e}"))
                self.root.after(0, lambda: self.update_button_state(
                    self.start_all_btn, "🚀 راه‌اندازی دستگاه‌ها", "normal"
                ))

        threading.Thread(target=start_thread, daemon=True).start()
    
    def stop_all_devices(self):
        """توقف همه دستگاه‌ها"""
        if not messagebox.askyesno("تأیید", "آیا از توقف همه دستگاه‌ها اطمینان دارید؟"):
            return
        
        self.stop_all_btn.config(state="disabled", text="در حال توقف...")
        self.status_bar.config(text="توقف همه دستگاه‌ها...")
        
        def stop_thread():
            try:
                stopped = self.farm.stop_all_devices(
                    progress_callback=self.update_progress
                )
                
                self.root.after(0, lambda: self.update_button_state(
                    self.stop_all_btn, "🛑 توقف همه دستگاه‌ها", "normal"
                ))
                
                self.root.after(0, lambda: self.status_bar.config(
                    text=f"✅ {stopped} دستگاه متوقف شد"
                ))
                
                self.root.after(0, lambda: messagebox.showinfo(
                    "اطلاعات", f"{stopped} دستگاه متوقف شد"
                ))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در توقف: {e}"))
                self.root.after(0, lambda: self.update_button_state(
                    self.stop_all_btn, "🛑 توقف همه دستگاه‌ها", "normal"
                ))
        
        threading.Thread(target=stop_thread, daemon=True).start()
    
    def install_apk(self):
        """نصب APK یا XAPK"""
        apk_path = filedialog.askopenfilename(
            title="انتخاب فایل APK یا XAPK",
            filetypes=[
                ("Android packages", "*.apk;*.xapk"),
                ("APK files", "*.apk"),
                ("XAPK files", "*.xapk"),
                ("All files", "*.*")
            ]
        )
        
        if apk_path:
            file_type = "XAPK" if apk_path.lower().endswith('.xapk') else "APK"
            self.install_apk_btn.config(state="disabled", text=f"در حال نصب {file_type}...")
            self.status_bar.config(text=f"نصب {file_type} روی همه دستگاه‌ها...")

            def install_thread():
                try:
                    successful = self.farm.install_apk_on_all(
                        apk_path,
                        progress_callback=self.update_progress
                    )

                    self.root.after(0, lambda: self.update_button_state(
                        self.install_apk_btn, "📦 نصب Kepithor (APK/XAPK)", "normal"
                    ))

                    self.root.after(0, lambda: self.status_bar.config(
                        text=f"✅ {file_type} روی {successful} دستگاه نصب شد"
                    ))

                    self.root.after(0, lambda: messagebox.showinfo(
                        "اطلاعات", f"{file_type} روی {successful} دستگاه نصب شد"
                    ))

                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در نصب: {e}"))
                    self.root.after(0, lambda: self.update_button_state(
                        self.install_apk_btn, "📦 نصب Kepithor (APK/XAPK)", "normal"
                    ))
            
            threading.Thread(target=install_thread, daemon=True).start()
    
    def launch_kepithor(self):
        """اجرای Kepithor"""
        self.launch_kepithor_btn.config(state="disabled", text="در حال اجرا...")
        self.status_bar.config(text="اجرای Kepithor روی همه دستگاه‌ها...")
        
        def launch_thread():
            try:
                successful = self.farm.launch_kepithor_on_all(
                    progress_callback=self.update_progress
                )
                
                self.root.after(0, lambda: self.update_button_state(
                    self.launch_kepithor_btn, "💰 اجرای Kepithor روی همه", "normal"
                ))
                
                self.root.after(0, lambda: self.status_bar.config(
                    text=f"✅ Kepithor روی {successful} دستگاه اجرا شد"
                ))
                
                self.root.after(0, lambda: messagebox.showinfo(
                    "اطلاعات", f"Kepithor روی {successful} دستگاه اجرا شد"
                ))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در اجرا: {e}"))
                self.root.after(0, lambda: self.update_button_state(
                    self.launch_kepithor_btn, "💰 اجرای Kepithor روی همه", "normal"
                ))
        
        threading.Thread(target=launch_thread, daemon=True).start()
    
    def close_kepithor(self):
        """بستن Kepithor روی همه دستگاه‌ها"""
        self.close_kepithor_btn.config(state="disabled", text="در حال بستن...")
        self.status_bar.config(text="بستن Kepithor روی همه دستگاه‌ها...")

        def close_thread():
            try:
                if self.use_real_emulators:
                    # برای شبیه‌سازهای واقعی
                    closed = self.farm.stop_kepithor_on_all(
                        progress_callback=self.update_progress
                    )
                else:
                    # برای شبیه‌سازهای فیک
                    closed = 0
                    for device_id, device in self.farm.running_devices.items():
                        if hasattr(device, 'current_app') and device.current_app == 'com.KepithorStudios.KKTFaucet':
                            device.go_home()  # بازگشت به صفحه اصلی
                            closed += 1

                self.root.after(0, lambda: self.update_button_state(
                    self.close_kepithor_btn, "❌ بستن Kepithor", "normal"
                ))

                self.root.after(0, lambda: self.status_bar.config(
                    text=f"✅ Kepithor روی {closed} دستگاه بسته شد"
                ))

                self.root.after(0, lambda: messagebox.showinfo(
                    "اطلاعات", f"Kepithor روی {closed} دستگاه بسته شد"
                ))

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطا", f"خطا در بستن: {e}"))
                self.root.after(0, lambda: self.update_button_state(
                    self.close_kepithor_btn, "❌ بستن Kepithor", "normal"
                ))

        threading.Thread(target=close_thread, daemon=True).start()

    def tile_windows(self):
        """چیدمان کاشی‌وار پنجره‌های دستگاه‌ها"""
        try:
            import tkinter as tk

            running_devices = list(self.farm.running_devices.values())
            if not running_devices:
                messagebox.showwarning("هشدار", "هیچ دستگاهی فعال نیست!")
                return

            # محاسبه ابعاد صفحه
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # محاسبه تعداد ردیف و ستون
            device_count = len(running_devices)
            cols = int(device_count ** 0.5) + 1
            rows = (device_count + cols - 1) // cols

            # محاسبه اندازه هر پنجره
            window_width = min(300, screen_width // cols - 10)
            window_height = min(500, screen_height // rows - 50)

            # چیدمان پنجره‌ها
            for i, device in enumerate(running_devices):
                if device.gui_window:
                    row = i // cols
                    col = i % cols

                    x = col * (window_width + 10) + 10
                    y = row * (window_height + 50) + 50

                    # تنظیم موقعیت و اندازه
                    device.gui_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
                    device.gui_window.lift()  # آوردن به جلو

            messagebox.showinfo("موفقیت", f"{device_count} پنجره به صورت کاشی‌وار چیده شد")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در چیدمان: {e}")

    def show_settings(self):
        """نمایش تنظیمات"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("⚙️ تنظیمات مزرعه")
        settings_window.geometry("400x300")
        settings_window.configure(bg="#2a2a2a")

        # عنوان
        title_label = tk.Label(
            settings_window,
            text="⚙️ تنظیمات مزرعه شبیه‌ساز",
            font=("Arial", 14, "bold"),
            bg="#2a2a2a",
            fg="white"
        )
        title_label.pack(pady=10)

        # تنظیمات
        settings_frame = tk.Frame(settings_window, bg="#2a2a2a")
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # حداکثر دستگاه
        tk.Label(settings_frame, text="حداکثر دستگاه:", bg="#2a2a2a", fg="white").pack(anchor=tk.W)
        max_devices_var = tk.StringVar(value=str(self.farm.max_devices))
        max_devices_entry = tk.Entry(settings_frame, textvariable=max_devices_var)
        max_devices_entry.pack(fill=tk.X, pady=5)

        # دکمه ذخیره
        def save_settings():
            try:
                new_max = int(max_devices_var.get())
                self.farm.max_devices = new_max
                messagebox.showinfo("موفقیت", "تنظیمات ذخیره شد")
                settings_window.destroy()
            except ValueError:
                messagebox.showerror("خطا", "عدد معتبر وارد کنید")

        save_btn = tk.Button(
            settings_frame,
            text="💾 ذخیره",
            command=save_settings,
            bg="#4CAF50",
            fg="white",
            font=("Arial", 10, "bold")
        )
        save_btn.pack(pady=10)

    def show_report(self):
        """نمایش گزارش"""
        stats = self.farm.get_system_stats()

        report = f"""
📊 گزارش عملکرد مزرعه شبیه‌ساز

📱 کل دستگاه‌ها: {stats['total_devices']}
🟢 دستگاه‌های فعال: {stats['running_devices']}
📦 APK نصب شده: {stats['installed_apps']}
💰 Kepithor فعال: {stats['active_kepithor']}

💾 استفاده از حافظه: {stats['memory_usage']:.1f}%
🔥 استفاده از CPU: {stats['cpu_usage']:.1f}%
💽 حافظه آزاد: {stats['memory_available_gb']:.1f} GB

⏰ زمان: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """

        messagebox.showinfo("گزارش عملکرد", report)
    
    def show_context_menu(self, event):
        """نمایش منوی راست کلیک"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def start_selected_device(self):
        """راه‌اندازی دستگاه انتخاب شده"""
        messagebox.showinfo("اطلاعات", "راه‌اندازی دستگاه انتخاب شده (در حال توسعه)")
    
    def stop_selected_device(self):
        """توقف دستگاه انتخاب شده"""
        messagebox.showinfo("اطلاعات", "توقف دستگاه انتخاب شده (در حال توسعه)")
    
    def install_apk_on_selected(self):
        """نصب APK روی دستگاه انتخاب شده"""
        messagebox.showinfo("اطلاعات", "نصب APK روی دستگاه انتخاب شده (در حال توسعه)")
    
    def launch_kepithor_on_selected(self):
        """اجرای Kepithor روی دستگاه انتخاب شده"""
        messagebox.showinfo("اطلاعات", "اجرای Kepithor روی دستگاه انتخاب شده (در حال توسعه)")
    
    def show_device_info(self):
        """نمایش اطلاعات دستگاه"""
        messagebox.showinfo("اطلاعات", "نمایش اطلاعات دستگاه (در حال توسعه)")
    
    def update_button_state(self, button, text, state):
        """به‌روزرسانی وضعیت دکمه"""
        button.config(text=text, state=state)
    
    def update_stats_timer(self):
        """به‌روزرسانی آمار به صورت دوره‌ای"""
        try:
            stats = self.farm.get_system_stats()
            
            # به‌روزرسانی برچسب‌های آمار
            self.total_devices_label.config(text=f"📱 کل دستگاه‌ها: {stats['total_devices']}")
            self.running_devices_label.config(text=f"🟢 دستگاه‌های فعال: {stats['running_devices']}")
            self.installed_apps_label.config(text=f"📦 APK نصب شده: {stats['installed_apps']}")
            self.active_kepithor_label.config(text=f"💰 Kepithor فعال: {stats['active_kepithor']}")
            self.memory_label.config(text=f"💾 حافظه: {stats['memory_usage']:.1f}%")
            self.cpu_label.config(text=f"🔥 CPU: {stats['cpu_usage']:.1f}%")
            
            # به‌روزرسانی جدول دستگاه‌ها
            self.update_devices_table()
            
        except Exception as e:
            print(f"❌ Error updating stats: {e}")
        
        # برنامه‌ریزی به‌روزرسانی بعدی
        self.root.after(3000, self.update_stats_timer)  # هر 3 ثانیه
    
    def update_devices_table(self):
        """به‌روزرسانی جدول دستگاه‌ها"""
        try:
            # پاک کردن آیتم‌های قبلی
            for item in self.devices_tree.get_children():
                self.devices_tree.delete(item)

            # اضافه کردن دستگاه‌ها
            if self.use_real_emulators:
                # برای شبیه‌سازهای واقعی
                devices_info = self.farm.get_emulators_info()

                for info in devices_info[:50]:
                    status = "🟢 Online" if info['is_running'] else "🔴 Offline"
                    kepithor_status = "💰 Active" if 'com.KepithorStudios.KKTFaucet' in info.get('installed_packages', []) else "⚫ Inactive"

                    self.devices_tree.insert("", "end", values=(
                        info['device_id'],
                        f"Port {info['port']}",
                        status,
                        info['avd_name'],
                        kepithor_status,
                        "Real Device"
                    ))
            else:
                # برای شبیه‌سازهای فیک
                devices_info = self.farm.get_devices_info()

                for info in devices_info[:50]:
                    status = "🟢 Online" if info['is_running'] else "🔴 Offline"
                    kepithor_status = "💰 Active" if info.get('current_app') == 'com.KepithorStudios.KKTFaucet' else "⚫ Inactive"

                    self.devices_tree.insert("", "end", values=(
                        info['device_id'],
                        info.get('ip_address', 'N/A'),
                        status,
                        info.get('specs', {}).get('name', 'Unknown'),
                        kepithor_status,
                        f"{info.get('specs', {}).get('ram_mb', 0)} MB"
                    ))

        except Exception as e:
            print(f"❌ Error updating devices table: {e}")
    
    def run(self):
        """اجرای رابط گرافیکی"""
        self.root.mainloop()

def main():
    """تابع اصلی"""
    print("🏭 Starting Android Emulator Farm GUI...")
    
    # اجرای رابط گرافیکی
    gui = EmulatorFarmGUI()
    gui.run()

if __name__ == "__main__":
    main()
