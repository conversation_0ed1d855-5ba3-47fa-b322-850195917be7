#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple APK Analyzer for Kepithor Rewards
تحلیل ساده فایل APK برای استخراج اطلاعات اولیه
"""

import os
import sys
import json
import zipfile
import xml.etree.ElementTree as ET
from datetime import datetime

class SimpleAPKAnalyzer:
    def __init__(self, apk_path):
        """Initialize simple APK analyzer"""
        self.apk_path = apk_path
        self.manifest_info = {}
        self.strings = []
        self.interesting_files = []
        
    def extract_manifest(self):
        """Extract AndroidManifest.xml information"""
        try:
            print("📋 Extracting AndroidManifest.xml...")
            
            with zipfile.ZipFile(self.apk_path, 'r') as apk_zip:
                # List all files in APK
                file_list = apk_zip.namelist()
                
                # Find interesting files
                for file_name in file_list:
                    if any(keyword in file_name.lower() for keyword in ['manifest', 'string', 'config', 'network', 'api']):
                        self.interesting_files.append(file_name)
                
                # Try to read AndroidManifest.xml (might be binary)
                if 'AndroidManifest.xml' in file_list:
                    manifest_data = apk_zip.read('AndroidManifest.xml')
                    
                    # Try to parse as text (unlikely but possible)
                    try:
                        manifest_text = manifest_data.decode('utf-8')
                        print("✅ Found text AndroidManifest.xml")
                        # Parse basic info from text
                        self.parse_text_manifest(manifest_text)
                    except UnicodeDecodeError:
                        print("⚠️ AndroidManifest.xml is binary (normal for APK)")
                        # Extract basic info from binary
                        self.parse_binary_manifest(manifest_data)
                
                # Extract strings from resources
                self.extract_strings_from_apk(apk_zip)
                
            return True
            
        except Exception as e:
            print(f"❌ Error extracting manifest: {e}")
            return False
    
    def parse_text_manifest(self, manifest_text):
        """Parse text AndroidManifest.xml"""
        try:
            root = ET.fromstring(manifest_text)
            
            # Extract package name
            self.manifest_info['package_name'] = root.get('package', 'Unknown')
            
            # Extract version info
            self.manifest_info['version_name'] = root.get('{http://schemas.android.com/apk/res/android}versionName', 'Unknown')
            self.manifest_info['version_code'] = root.get('{http://schemas.android.com/apk/res/android}versionCode', 'Unknown')
            
            # Extract permissions
            permissions = []
            for perm in root.findall('.//uses-permission'):
                perm_name = perm.get('{http://schemas.android.com/apk/res/android}name')
                if perm_name:
                    permissions.append(perm_name)
            self.manifest_info['permissions'] = permissions
            
            # Extract activities
            activities = []
            for activity in root.findall('.//activity'):
                activity_name = activity.get('{http://schemas.android.com/apk/res/android}name')
                if activity_name:
                    activities.append(activity_name)
            self.manifest_info['activities'] = activities
            
        except Exception as e:
            print(f"⚠️ Error parsing text manifest: {e}")
    
    def parse_binary_manifest(self, manifest_data):
        """Extract basic info from binary AndroidManifest.xml"""
        try:
            # Look for package name pattern in binary data
            data_str = str(manifest_data)
            
            # Try to find package name
            if 'com.KepithorStudios.KKTFaucet' in data_str:
                self.manifest_info['package_name'] = 'com.KepithorStudios.KKTFaucet'
            
            # Set basic info
            self.manifest_info['version_name'] = 'Unknown (binary manifest)'
            self.manifest_info['version_code'] = 'Unknown (binary manifest)'
            self.manifest_info['permissions'] = ['Unknown (binary manifest)']
            self.manifest_info['activities'] = ['Unknown (binary manifest)']
            
        except Exception as e:
            print(f"⚠️ Error parsing binary manifest: {e}")
    
    def extract_strings_from_apk(self, apk_zip):
        """Extract strings from APK files"""
        try:
            print("📝 Extracting strings from APK...")
            
            # Look for strings.xml files
            for file_name in apk_zip.namelist():
                if 'strings.xml' in file_name or 'string' in file_name.lower():
                    try:
                        file_data = apk_zip.read(file_name)
                        file_text = file_data.decode('utf-8', errors='ignore')
                        
                        # Extract interesting strings
                        lines = file_text.split('\n')
                        for line in lines:
                            if any(keyword in line.lower() for keyword in ['http', 'api', 'url', 'server', 'endpoint', 'token', 'key']):
                                self.strings.append(line.strip())
                                
                    except Exception as e:
                        continue
            
            # Also look in other files
            for file_name in apk_zip.namelist():
                if file_name.endswith('.xml') or file_name.endswith('.json') or file_name.endswith('.txt'):
                    try:
                        file_data = apk_zip.read(file_name)
                        file_text = file_data.decode('utf-8', errors='ignore')
                        
                        # Look for URLs and API endpoints
                        import re
                        urls = re.findall(r'https?://[^\s<>"]+', file_text)
                        for url in urls:
                            if url not in self.strings:
                                self.strings.append(url)
                                
                    except Exception as e:
                        continue
            
            print(f"✅ Found {len(self.strings)} interesting strings")
            
        except Exception as e:
            print(f"❌ Error extracting strings: {e}")
    
    def analyze_file_structure(self):
        """Analyze APK file structure"""
        try:
            print("📁 Analyzing file structure...")
            
            with zipfile.ZipFile(self.apk_path, 'r') as apk_zip:
                file_list = apk_zip.namelist()
                
                # Categorize files
                categories = {
                    'dex_files': [f for f in file_list if f.endswith('.dex')],
                    'native_libs': [f for f in file_list if f.startswith('lib/')],
                    'resources': [f for f in file_list if f.startswith('res/')],
                    'assets': [f for f in file_list if f.startswith('assets/')],
                    'meta_files': [f for f in file_list if f.startswith('META-INF/')],
                    'other_files': []
                }
                
                for f in file_list:
                    if not any(f in cat for cat in categories.values()):
                        categories['other_files'].append(f)
                
                self.file_structure = categories
                
                print(f"📱 DEX files: {len(categories['dex_files'])}")
                print(f"🔧 Native libraries: {len(categories['native_libs'])}")
                print(f"🎨 Resources: {len(categories['resources'])}")
                print(f"📦 Assets: {len(categories['assets'])}")
                print(f"📋 Meta files: {len(categories['meta_files'])}")
                print(f"📄 Other files: {len(categories['other_files'])}")
                
            return True
            
        except Exception as e:
            print(f"❌ Error analyzing file structure: {e}")
            return False
    
    def generate_report(self):
        """Generate analysis report"""
        try:
            print("📊 Generating analysis report...")
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'apk_path': self.apk_path,
                'manifest_info': self.manifest_info,
                'interesting_files': self.interesting_files,
                'strings': self.strings[:100],  # First 100 strings
                'file_structure': getattr(self, 'file_structure', {}),
                'analysis_notes': [
                    "This is a basic analysis of the APK file",
                    "For deeper analysis, use specialized tools like jadx or apktool",
                    "Binary AndroidManifest.xml requires special parsing",
                    "DEX files contain the actual application code"
                ]
            }
            
            # Save report to file
            report_file = f"simple_apk_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Report saved to: {report_file}")
            return report_file
            
        except Exception as e:
            print(f"❌ Error generating report: {e}")
            return None
    
    def analyze(self):
        """Run complete analysis"""
        print("🚀 Starting simple APK analysis...")
        
        if not os.path.exists(self.apk_path):
            print(f"❌ APK file not found: {self.apk_path}")
            return False
        
        # Extract manifest info
        if not self.extract_manifest():
            return False
        
        # Analyze file structure
        self.analyze_file_structure()
        
        # Generate report
        report_file = self.generate_report()
        
        # Print summary
        print("\n🎯 Analysis Summary:")
        print("=" * 50)
        print(f"📦 Package: {self.manifest_info.get('package_name', 'Unknown')}")
        print(f"🔢 Version: {self.manifest_info.get('version_name', 'Unknown')}")
        print(f"📝 Interesting files: {len(self.interesting_files)}")
        print(f"🔤 Strings found: {len(self.strings)}")
        
        if self.strings:
            print("\n🔍 Sample interesting strings:")
            for i, string in enumerate(self.strings[:10]):
                print(f"  {i+1}. {string}")
        
        print(f"\n📄 Full report: {report_file}")
        
        return True

def main():
    """Main function"""
    print("🔍 Simple Kepithor APK Analyzer")
    print("=" * 50)
    
    # Check if APK file is provided
    if len(sys.argv) < 2:
        print("Usage: python simple_apk_analyzer.py <path_to_apk>")
        return
    
    apk_path = sys.argv[1]
    
    # Run analysis
    analyzer = SimpleAPKAnalyzer(apk_path)
    success = analyzer.analyze()
    
    if success:
        print("\n🎉 Analysis completed successfully!")
        print("\nNext steps:")
        print("1. Review the generated JSON report")
        print("2. Look for API endpoints in the strings")
        print("3. Use network monitoring tools to capture real traffic")
        print("4. Implement API simulation based on findings")
    else:
        print("\n❌ Analysis failed!")

if __name__ == "__main__":
    main()
