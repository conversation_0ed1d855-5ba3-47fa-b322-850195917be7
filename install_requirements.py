#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Install Requirements for OCR App
نصب کتابخانه‌های مورد نیاز برای OCR App
"""

import subprocess
import sys
import os

def check_python_version():
    """بررسی نسخه Python"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required!")
        return False
    
    print("✅ Python version is compatible")
    return True

def install_package(package):
    """نصب یک کتابخانه"""
    try:
        print(f"📦 Installing {package}...")
        result = subprocess.run(
            [sys.executable, '-m', 'pip', 'install', package],
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}")
        print(f"Error: {e.stderr}")
        return False

def check_package(package):
    """بررسی نصب بودن کتابخانه"""
    try:
        __import__(package)
        print(f"✅ {package} is already installed")
        return True
    except ImportError:
        print(f"❌ {package} is not installed")
        return False

def install_requirements():
    """نصب همه کتابخانه‌های مورد نیاز"""
    print("🚀 OCR App Requirements Installer")
    print("=" * 50)
    
    # بررسی نسخه Python
    if not check_python_version():
        return False
    
    # لیست کتابخانه‌های مورد نیاز
    requirements = [
        ('Pillow', 'PIL'),           # پردازش تصویر
        ('pytesseract', 'pytesseract'),  # OCR
        ('pyautogui', 'pyautogui'),      # اتوماسیون GUI
        ('opencv-python', 'cv2'),        # بینایی کامپیوتر
        ('numpy', 'numpy'),              # محاسبات عددی
        ('psutil', 'psutil'),            # اطلاعات سیستم
    ]
    
    print("\n🔍 Checking installed packages...")
    
    # بررسی کتابخانه‌های نصب شده
    to_install = []
    for pip_name, import_name in requirements:
        if not check_package(import_name):
            to_install.append(pip_name)
    
    if not to_install:
        print("\n🎉 All required packages are already installed!")
        return True
    
    print(f"\n📦 Installing {len(to_install)} packages...")
    
    # نصب کتابخانه‌های مورد نیاز
    failed_packages = []
    for package in to_install:
        if not install_package(package):
            failed_packages.append(package)
    
    # نتیجه نصب
    if failed_packages:
        print(f"\n❌ Failed to install: {', '.join(failed_packages)}")
        print("Please install them manually:")
        for package in failed_packages:
            print(f"  pip install {package}")
        return False
    else:
        print("\n🎉 All packages installed successfully!")
        return True

def install_from_requirements_file():
    """نصب از فایل requirements.txt"""
    if os.path.exists('requirements.txt'):
        print("\n📄 Installing from requirements.txt...")
        try:
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
                capture_output=True,
                text=True,
                check=True
            )
            print("✅ Requirements installed from file")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install from requirements.txt")
            print(f"Error: {e.stderr}")
            return False
    else:
        print("⚠️ requirements.txt not found")
        return False

def check_external_dependencies():
    """بررسی وابستگی‌های خارجی"""
    print("\n🔍 Checking external dependencies...")
    
    # بررسی Tesseract OCR
    tesseract_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        "tesseract"  # در PATH
    ]
    
    tesseract_found = False
    for path in tesseract_paths:
        if path == "tesseract":
            try:
                result = subprocess.run(['tesseract', '--version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✅ Tesseract OCR found in PATH")
                    tesseract_found = True
                    break
            except FileNotFoundError:
                continue
        else:
            if os.path.exists(path):
                print(f"✅ Tesseract OCR found at: {path}")
                tesseract_found = True
                break
    
    if not tesseract_found:
        print("❌ Tesseract OCR not found!")
        print("📥 Download from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("📁 Install to: C:\\Program Files\\Tesseract-OCR\\")
    
    # بررسی ADB
    adb_paths = [
        "adb.exe",
        "adb/adb.exe",
        "adb"  # در PATH
    ]
    
    adb_found = False
    for path in adb_paths:
        if path == "adb":
            try:
                result = subprocess.run(['adb', 'version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✅ ADB found in PATH")
                    adb_found = True
                    break
            except FileNotFoundError:
                continue
        else:
            if os.path.exists(path):
                print(f"✅ ADB found at: {path}")
                adb_found = True
                break
    
    if not adb_found:
        print("❌ ADB not found!")
        print("📥 Download Android SDK Platform Tools")
        print("📁 Place adb.exe in the app directory")
    
    return tesseract_found and adb_found

def test_imports():
    """تست import کتابخانه‌ها"""
    print("\n🧪 Testing imports...")
    
    test_cases = [
        ('tkinter', 'tkinter'),
        ('PIL', 'Pillow'),
        ('pytesseract', 'pytesseract'),
        ('pyautogui', 'pyautogui'),
        ('cv2', 'opencv-python'),
        ('numpy', 'numpy'),
        ('psutil', 'psutil'),
    ]
    
    failed_imports = []
    for module, package in test_cases:
        try:
            __import__(module)
            print(f"✅ {module} import successful")
        except ImportError as e:
            print(f"❌ {module} import failed: {e}")
            failed_imports.append(package)
    
    if failed_imports:
        print(f"\n❌ Failed imports: {', '.join(failed_imports)}")
        return False
    else:
        print("\n🎉 All imports successful!")
        return True

def main():
    """تابع اصلی"""
    print("🔧 OCR App Dependencies Installer")
    print("نصب وابستگی‌های OCR App")
    print("=" * 60)
    
    success = True
    
    # نصب کتابخانه‌ها
    if not install_requirements():
        success = False
    
    # نصب از فایل requirements
    if not install_from_requirements_file():
        print("⚠️ Continuing with manual installation...")
    
    # بررسی وابستگی‌های خارجی
    if not check_external_dependencies():
        success = False
    
    # تست import ها
    if not test_imports():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Installation completed successfully!")
        print("✅ OCR App is ready to run!")
    else:
        print("⚠️ Installation completed with warnings!")
        print("❌ Some dependencies may be missing!")
        print("📖 Please check the messages above and install missing components")
    
    print("\n📋 Summary:")
    print("• Python packages: Install with pip")
    print("• Tesseract OCR: Download and install separately")
    print("• ADB: Place in app directory")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
