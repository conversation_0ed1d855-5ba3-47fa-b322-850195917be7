#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test Real Android Emulator
تست شبیه‌ساز واقعی اندروید
"""

import os
import sys
import time
from pathlib import Path

def test_single_real_emulator():
    """تست یک شبیه‌ساز واقعی"""
    print("🧪 Testing Single Real Android Emulator")
    print("=" * 50)
    
    try:
        from real_android_emulator import RealAndroidEmulator
        
        # ایجاد شبیه‌ساز
        print("1. Creating real emulator...")
        emulator = RealAndroidEmulator(device_id=1)
        
        # راه‌اندازی
        print("2. Starting emulator (this may take 5-10 minutes)...")
        if emulator.start_emulator():
            print("✅ Emulator started successfully!")
            
            # نصب XAPK
            xapk_path = "Kepithor Rewards_159_APKPure.xapk"
            if os.path.exists(xapk_path):
                print("3. Installing Kepithor XAPK...")
                
                # استخراج XAPK
                import zipfile
                extract_dir = Path("temp_test_xapk")
                extract_dir.mkdir(exist_ok=True)
                
                with zipfile.ZipFile(xapk_path, 'r') as xapk:
                    xapk.extractall(extract_dir)
                
                # پیدا کردن APK اصلی
                apk_files = list(extract_dir.glob("*.apk"))
                if apk_files:
                    main_apk = max(apk_files, key=lambda x: x.stat().st_size)
                    print(f"   Found main APK: {main_apk}")
                    
                    if emulator.install_apk(str(main_apk)):
                        print("✅ Kepithor installed successfully!")
                        
                        # اجرای Kepithor
                        print("4. Launching Kepithor...")
                        if emulator.launch_app('com.KepithorStudios.KKTFaucet'):
                            print("✅ Kepithor launched successfully!")
                            
                            # گرفتن اسکرین‌شات
                            print("5. Taking screenshot...")
                            screenshot_path = emulator.take_screenshot("kepithor_running.png")
                            if screenshot_path:
                                print(f"✅ Screenshot saved: {screenshot_path}")
                            
                            # نگه داشتن برای مشاهده
                            print("\n🎉 Test completed successfully!")
                            print("Kepithor is now running on real Android emulator!")
                            input("Press Enter to shutdown emulator...")
                        else:
                            print("❌ Failed to launch Kepithor")
                    else:
                        print("❌ Failed to install Kepithor")
                else:
                    print("❌ No APK files found in XAPK")
            else:
                print("⚠️ XAPK file not found, skipping installation")
            
            # خاموش کردن
            print("6. Shutting down emulator...")
            emulator.shutdown()
            print("✅ Test completed")
            
        else:
            print("❌ Failed to start emulator")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_real_emulator_manager():
    """تست مدیر شبیه‌سازهای واقعی"""
    print("🧪 Testing Real Emulator Manager")
    print("=" * 50)
    
    try:
        from real_emulator_manager import RealEmulatorManager
        
        # ایجاد مدیر با 2 شبیه‌ساز
        print("1. Creating manager for 2 emulators...")
        manager = RealEmulatorManager(max_devices=2)
        
        # راه‌اندازی
        print("2. Starting emulators (this may take 10-15 minutes)...")
        successful = manager.start_all_emulators()
        
        if successful > 0:
            print(f"✅ {successful} emulators started!")
            
            # نمایش آمار
            manager.print_status()
            
            # نصب XAPK
            xapk_path = "Kepithor Rewards_159_APKPure.xapk"
            if os.path.exists(xapk_path):
                print("3. Installing Kepithor on all emulators...")
                installed = manager.install_apk_on_all(xapk_path)
                print(f"✅ Kepithor installed on {installed} emulators")
                
                # اجرای Kepithor
                print("4. Launching Kepithor on all emulators...")
                launched = manager.launch_kepithor_on_all()
                print(f"✅ Kepithor launched on {launched} emulators")
                
                # گرفتن اسکرین‌شات
                print("5. Taking screenshots...")
                screenshots = manager.take_screenshots_all()
                print(f"✅ {screenshots} screenshots taken")
            
            # نمایش آمار نهایی
            manager.print_status()
            
            # نگه داشتن
            input("Press Enter to shutdown all emulators...")
            
            # توقف
            manager.stop_all_emulators()
            
        else:
            print("❌ No emulators started")
        
        return True
        
    except Exception as e:
        print(f"❌ Manager test error: {e}")
        return False

def check_prerequisites():
    """بررسی پیش‌نیازها"""
    print("🔍 Checking prerequisites...")
    
    # بررسی فایل XAPK
    xapk_path = "Kepithor Rewards_159_APKPure.xapk"
    if not os.path.exists(xapk_path):
        print(f"❌ XAPK file not found: {xapk_path}")
        print("Please make sure the XAPK file is in the current directory")
        return False
    else:
        print(f"✅ XAPK file found: {xapk_path}")
    
    # بررسی فضای دیسک
    import shutil
    free_space = shutil.disk_usage('.').free / (1024**3)
    if free_space < 5:
        print(f"⚠️ Low disk space: {free_space:.1f} GB")
        print("At least 5GB free space recommended")
    else:
        print(f"✅ Disk space: {free_space:.1f} GB available")
    
    # بررسی RAM
    try:
        import psutil
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)
        
        if available_gb < 4:
            print(f"⚠️ Low memory: {available_gb:.1f} GB available")
            print("At least 4GB RAM recommended for real emulators")
        else:
            print(f"✅ Memory: {available_gb:.1f} GB available")
    except ImportError:
        print("⚠️ Cannot check memory (psutil not installed)")
    
    return True

def main():
    """منوی اصلی تست"""
    print("🧪 Real Android Emulator Test Suite")
    print("=" * 60)
    
    # بررسی پیش‌نیازها
    if not check_prerequisites():
        input("Press Enter to exit...")
        return
    
    print("\nTest Options:")
    print("1. Test single real emulator")
    print("2. Test real emulator manager (2 emulators)")
    print("3. Check system requirements")
    print("4. Exit")
    
    while True:
        try:
            choice = input("\nSelect test (1-4): ").strip()
            
            if choice == "1":
                print("\n⚠️ WARNING: This test will take 5-10 minutes!")
                confirm = input("Continue? (y/n): ").strip().lower()
                if confirm == 'y':
                    test_single_real_emulator()
            
            elif choice == "2":
                print("\n⚠️ WARNING: This test will take 10-15 minutes!")
                confirm = input("Continue? (y/n): ").strip().lower()
                if confirm == 'y':
                    test_real_emulator_manager()
            
            elif choice == "3":
                try:
                    import psutil
                    
                    cpu_count = psutil.cpu_count()
                    memory = psutil.virtual_memory()
                    disk = psutil.disk_usage('.')
                    
                    print(f"\n🖥️ System Requirements Check:")
                    print(f"CPU Cores: {cpu_count}")
                    print(f"Total RAM: {memory.total / (1024**3):.1f} GB")
                    print(f"Available RAM: {memory.available / (1024**3):.1f} GB")
                    print(f"Free Disk Space: {disk.free / (1024**3):.1f} GB")
                    
                    print(f"\n📊 Recommendations:")
                    print(f"• Real emulators: {min(5, cpu_count // 2)} concurrent devices")
                    print(f"• Each emulator needs ~2GB RAM")
                    print(f"• Each emulator needs ~2GB disk space")
                    
                    if memory.available < 4 * (1024**3):
                        print("⚠️ Warning: Low available RAM for real emulators")
                    
                    if cpu_count < 4:
                        print("⚠️ Warning: Low CPU cores for multiple emulators")
                    
                except ImportError:
                    print("❌ Cannot check system requirements (psutil not installed)")
            
            elif choice == "4":
                print("👋 Goodbye!")
                break
            
            else:
                print("❌ Invalid choice!")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
