#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Real Android Emulator using Android SDK
شبیه‌ساز واقعی اندروید با استفاده از Android SDK
"""

import os
import sys
import json
import time
import subprocess
import threading
import zipfile
from pathlib import Path
import requests
import shutil

class RealAndroidEmulator:
    """شبیه‌ساز واقعی اندروید"""
    
    def __init__(self, device_id, avd_name=None):
        """راه‌اندازی شبیه‌ساز واقعی"""
        self.device_id = device_id
        self.avd_name = avd_name or f"Kepithor_Device_{device_id}"
        self.port = 5554 + (device_id * 2)  # هر دستگاه 2 port می‌خواد
        
        # مسیرهای Android SDK
        self.setup_android_paths()
        
        # وضعیت دستگاه
        self.is_running = False
        self.adb_device_name = f"emulator-{self.port}"
        self.emulator_process = None
        
        # اطلاعات نصب شده
        self.installed_packages = set()
        
        print(f"📱 Real Android Emulator {device_id} initialized")
    
    def setup_android_paths(self):
        """تنظیم مسیرهای Android SDK"""
        # جستجوی Android SDK در مسیرهای معمول
        possible_sdk_paths = [
            os.path.expanduser("~/Android/Sdk"),
            os.path.expanduser("~/AppData/Local/Android/Sdk"),
            "C:/Android/Sdk",
            "C:/Users/<USER>/AppData/Local/Android/Sdk",
            "/opt/android-sdk",
            "/usr/local/android-sdk"
        ]
        
        self.android_sdk_path = None
        for path in possible_sdk_paths:
            if os.path.exists(path):
                self.android_sdk_path = path
                break
        
        if not self.android_sdk_path:
            # دانلود و نصب خودکار Android SDK
            self.download_android_sdk()
        
        # تنظیم مسیرهای ابزارها
        self.emulator_path = os.path.join(self.android_sdk_path, "emulator", "emulator")
        self.adb_path = os.path.join(self.android_sdk_path, "platform-tools", "adb")
        self.avdmanager_path = os.path.join(self.android_sdk_path, "cmdline-tools", "latest", "bin", "avdmanager")
        
        # تنظیم متغیرهای محیطی
        os.environ["ANDROID_SDK_ROOT"] = self.android_sdk_path
        os.environ["ANDROID_HOME"] = self.android_sdk_path
        
        print(f"📁 Android SDK path: {self.android_sdk_path}")
    
    def download_android_sdk(self):
        """دانلود و نصب Android SDK"""
        print("📥 Downloading Android SDK...")
        
        # ایجاد فولدر SDK
        self.android_sdk_path = os.path.join(os.getcwd(), "android_sdk")
        os.makedirs(self.android_sdk_path, exist_ok=True)
        
        # دانلود command line tools
        if os.name == 'nt':  # Windows
            sdk_url = "https://dl.google.com/android/repository/commandlinetools-win-9477386_latest.zip"
        else:  # Linux/Mac
            sdk_url = "https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip"
        
        try:
            print("⬇️ Downloading Android Command Line Tools...")
            response = requests.get(sdk_url, stream=True)
            
            zip_path = os.path.join(self.android_sdk_path, "cmdline-tools.zip")
            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # استخراج
            print("📦 Extracting Android Command Line Tools...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.android_sdk_path)
            
            # تنظیم ساختار فولدر
            cmdline_tools_path = os.path.join(self.android_sdk_path, "cmdline-tools")
            latest_path = os.path.join(cmdline_tools_path, "latest")
            
            if not os.path.exists(latest_path):
                extracted_path = os.path.join(cmdline_tools_path, "cmdline-tools")
                if os.path.exists(extracted_path):
                    shutil.move(extracted_path, latest_path)
            
            # حذف فایل zip
            os.remove(zip_path)
            
            print("✅ Android SDK downloaded successfully")
            
        except Exception as e:
            print(f"❌ Error downloading Android SDK: {e}")
            raise
    
    def install_sdk_components(self):
        """نصب اجزای مورد نیاز SDK"""
        print("📦 Installing required SDK components...")
        
        # لیست اجزای مورد نیاز
        components = [
            "platform-tools",
            "emulator",
            "platforms;android-30",
            "system-images;android-30;google_apis;x86_64"
        ]
        
        sdkmanager_path = os.path.join(
            self.android_sdk_path, "cmdline-tools", "latest", "bin", 
            "sdkmanager.bat" if os.name == 'nt' else "sdkmanager"
        )
        
        for component in components:
            try:
                print(f"📥 Installing {component}...")
                cmd = [sdkmanager_path, component]
                
                # اجرای دستور با پذیرش خودکار لایسنس
                process = subprocess.Popen(
                    cmd,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                # ارسال 'y' برای پذیرش لایسنس
                stdout, stderr = process.communicate(input='y\n' * 10)
                
                if process.returncode == 0:
                    print(f"✅ {component} installed successfully")
                else:
                    print(f"⚠️ {component} installation warning: {stderr}")
                    
            except Exception as e:
                print(f"❌ Error installing {component}: {e}")
    
    def create_avd(self):
        """ایجاد Android Virtual Device"""
        try:
            print(f"📱 Creating AVD: {self.avd_name}")
            
            # بررسی وجود AVD
            if self.avd_exists():
                print(f"✅ AVD {self.avd_name} already exists")
                return True
            
            # ایجاد AVD جدید
            cmd = [
                self.avdmanager_path,
                "create", "avd",
                "-n", self.avd_name,
                "-k", "system-images;android-30;google_apis;x86_64",
                "-d", "pixel_3a",
                "--force"
            ]
            
            process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # پاسخ به سوالات AVD
            responses = "no\n" * 5  # پاسخ منفی به سوالات اضافی
            stdout, stderr = process.communicate(input=responses)
            
            if process.returncode == 0:
                print(f"✅ AVD {self.avd_name} created successfully")
                return True
            else:
                print(f"❌ AVD creation failed: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error creating AVD: {e}")
            return False
    
    def avd_exists(self):
        """بررسی وجود AVD"""
        try:
            cmd = [self.avdmanager_path, "list", "avd"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            return self.avd_name in result.stdout
        except:
            return False
    
    def start_emulator(self):
        """راه‌اندازی شبیه‌ساز"""
        try:
            if self.is_running:
                print(f"⚠️ Emulator {self.device_id} already running")
                return True
            
            print(f"🚀 Starting emulator {self.device_id}...")
            
            # نصب اجزای SDK در صورت نیاز
            if not os.path.exists(self.emulator_path):
                self.install_sdk_components()
            
            # ایجاد AVD در صورت نیاز
            if not self.create_avd():
                return False
            
            # راه‌اندازی emulator
            cmd = [
                self.emulator_path,
                "-avd", self.avd_name,
                "-port", str(self.port),
                "-no-audio",
                "-no-window",  # بدون پنجره گرافیکی
                "-gpu", "off",
                "-memory", "2048",
                "-partition-size", "4096"
            ]
            
            print(f"🔧 Emulator command: {' '.join(cmd)}")
            
            # اجرای emulator در background
            self.emulator_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # انتظار برای boot شدن
            print("⏳ Waiting for emulator to boot...")
            if self.wait_for_boot():
                self.is_running = True
                print(f"✅ Emulator {self.device_id} started successfully")
                return True
            else:
                print(f"❌ Emulator {self.device_id} failed to boot")
                return False
                
        except Exception as e:
            print(f"❌ Error starting emulator: {e}")
            return False
    
    def wait_for_boot(self, timeout=300):
        """انتظار برای boot شدن emulator"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # بررسی وضعیت boot
                cmd = [self.adb_path, "-s", self.adb_device_name, "shell", "getprop", "sys.boot_completed"]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0 and "1" in result.stdout:
                    print("✅ Emulator booted successfully")
                    return True
                    
            except subprocess.TimeoutExpired:
                pass
            except Exception:
                pass
            
            print("⏳ Still booting...")
            time.sleep(10)
        
        print("❌ Boot timeout")
        return False
    
    def install_apk(self, apk_path):
        """نصب واقعی APK"""
        try:
            if not self.is_running:
                print("❌ Emulator not running")
                return False
            
            if not os.path.exists(apk_path):
                print(f"❌ APK file not found: {apk_path}")
                return False
            
            print(f"📦 Installing APK: {apk_path}")
            
            # نصب APK با adb
            cmd = [self.adb_path, "-s", self.adb_device_name, "install", "-r", apk_path]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0 and "Success" in result.stdout:
                print(f"✅ APK installed successfully on device {self.device_id}")
                
                # استخراج package name
                package_name = self.get_package_name(apk_path)
                if package_name:
                    self.installed_packages.add(package_name)
                
                return True
            else:
                print(f"❌ APK installation failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error installing APK: {e}")
            return False
    
    def get_package_name(self, apk_path):
        """استخراج package name از APK"""
        try:
            # استفاده از aapt برای استخراج package name
            aapt_path = os.path.join(self.android_sdk_path, "build-tools", "*", "aapt")
            
            # جستجوی aapt
            import glob
            aapt_files = glob.glob(aapt_path)
            if aapt_files:
                aapt_path = aapt_files[0]
                
                cmd = [aapt_path, "dump", "badging", apk_path]
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                for line in result.stdout.split('\n'):
                    if line.startswith('package:'):
                        # استخراج package name
                        import re
                        match = re.search(r"name='([^']+)'", line)
                        if match:
                            return match.group(1)
            
            # روش جایگزین با zipfile
            with zipfile.ZipFile(apk_path, 'r') as apk_zip:
                if 'AndroidManifest.xml' in apk_zip.namelist():
                    # برای Kepithor
                    if 'Kepithor' in apk_path:
                        return 'com.KepithorStudios.KKTFaucet'
            
            return None
            
        except Exception as e:
            print(f"❌ Error extracting package name: {e}")
            return None
    
    def launch_app(self, package_name):
        """اجرای واقعی برنامه"""
        try:
            if not self.is_running:
                print("❌ Emulator not running")
                return False
            
            if package_name not in self.installed_packages:
                print(f"❌ Package not installed: {package_name}")
                return False
            
            print(f"🚀 Launching app: {package_name}")
            
            # اجرای برنامه
            cmd = [
                self.adb_path, "-s", self.adb_device_name, 
                "shell", "monkey", "-p", package_name, "-c", "android.intent.category.LAUNCHER", "1"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ App launched successfully: {package_name}")
                return True
            else:
                print(f"❌ App launch failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error launching app: {e}")
            return False
    
    def stop_app(self, package_name):
        """توقف برنامه"""
        try:
            cmd = [self.adb_path, "-s", self.adb_device_name, "shell", "am", "force-stop", package_name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ App stopped: {package_name}")
                return True
            else:
                print(f"❌ Failed to stop app: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error stopping app: {e}")
            return False
    
    def get_screen_size(self):
        """دریافت اندازه صفحه"""
        try:
            cmd = [self.adb_path, "-s", self.adb_device_name, "shell", "wm", "size"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # پارس کردن خروجی: Physical size: 1080x1920
                import re
                match = re.search(r'(\d+)x(\d+)', result.stdout)
                if match:
                    return int(match.group(1)), int(match.group(2))
            
            return 1080, 1920  # پیش‌فرض
            
        except Exception as e:
            print(f"❌ Error getting screen size: {e}")
            return 1080, 1920
    
    def take_screenshot(self, save_path=None):
        """گرفتن اسکرین‌شات"""
        try:
            if not save_path:
                save_path = f"screenshot_device_{self.device_id}.png"
            
            cmd = [self.adb_path, "-s", self.adb_device_name, "shell", "screencap", "/sdcard/screenshot.png"]
            subprocess.run(cmd, check=True)
            
            cmd = [self.adb_path, "-s", self.adb_device_name, "pull", "/sdcard/screenshot.png", save_path]
            subprocess.run(cmd, check=True)
            
            print(f"📸 Screenshot saved: {save_path}")
            return save_path
            
        except Exception as e:
            print(f"❌ Error taking screenshot: {e}")
            return None
    
    def tap_screen(self, x, y):
        """ضربه زدن روی صفحه"""
        try:
            cmd = [self.adb_path, "-s", self.adb_device_name, "shell", "input", "tap", str(x), str(y)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"👆 Tapped at ({x}, {y})")
                return True
            else:
                print(f"❌ Tap failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error tapping screen: {e}")
            return False
    
    def shutdown(self):
        """خاموش کردن شبیه‌ساز"""
        try:
            print(f"🔌 Shutting down emulator {self.device_id}...")
            
            # توقف emulator
            if self.emulator_process:
                self.emulator_process.terminate()
                self.emulator_process.wait(timeout=30)
            
            # kill adb connection
            cmd = [self.adb_path, "-s", self.adb_device_name, "emu", "kill"]
            subprocess.run(cmd, capture_output=True, text=True)
            
            self.is_running = False
            print(f"✅ Emulator {self.device_id} shutdown complete")
            
        except Exception as e:
            print(f"❌ Error shutting down emulator: {e}")
    
    def get_device_info(self):
        """دریافت اطلاعات دستگاه"""
        return {
            'device_id': self.device_id,
            'avd_name': self.avd_name,
            'port': self.port,
            'adb_device_name': self.adb_device_name,
            'is_running': self.is_running,
            'installed_packages': list(self.installed_packages)
        }

def main():
    """تست شبیه‌ساز واقعی"""
    print("🤖 Real Android Emulator Test")
    print("=" * 50)
    
    # ایجاد شبیه‌ساز
    emulator = RealAndroidEmulator(device_id=1)
    
    try:
        # راه‌اندازی
        if emulator.start_emulator():
            print("✅ Emulator started successfully!")
            
            # نصب APK
            apk_path = "Kepithor Rewards_159_APKPure.xapk"
            if os.path.exists(apk_path):
                # استخراج APK از XAPK
                print("📦 Extracting XAPK...")
                with zipfile.ZipFile(apk_path, 'r') as xapk:
                    xapk.extractall("temp_xapk")
                
                # پیدا کردن APK اصلی
                apk_files = list(Path("temp_xapk").glob("*.apk"))
                if apk_files:
                    main_apk = max(apk_files, key=lambda x: x.stat().st_size)
                    
                    if emulator.install_apk(str(main_apk)):
                        print("✅ Kepithor installed successfully!")
                        
                        # اجرای Kepithor
                        if emulator.launch_app('com.KepithorStudios.KKTFaucet'):
                            print("✅ Kepithor launched successfully!")
                            
                            # گرفتن اسکرین‌شات
                            emulator.take_screenshot()
                            
                            input("Press Enter to shutdown...")
            
            # خاموش کردن
            emulator.shutdown()
        
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        emulator.shutdown()

if __name__ == "__main__":
    main()
